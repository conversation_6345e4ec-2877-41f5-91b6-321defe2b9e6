use async_trait::async_trait;
use dashmap::DashMap;
use parking_lot::Mutex;
use rusqlite::{Connection, OpenFlags};
use std::{collections::HashMap, path::PathBuf, sync::Arc, time::Duration};
use tokio::{sync::RwLock, time::interval};

use crate::{
    collection_cache::Manager as CollectionCacheManager,
    config::FollowerConfig,
    db::{OplogEntry, OplogRepository, PostgresDatabase},
    shape::Shape,
    Result,
};

pub mod sqlite_helpers;

use sqlite_helpers::*;

/// Interface for follower functionality
#[async_trait]
pub trait FollowerInterface {
    async fn start(self: Arc<Self>, ctx: tokio_util::sync::CancellationToken) -> Result<()>;
    async fn ensure_open_connection(&self, collection_name: &str, version: i32) -> Result<()>;
}

/// Represents a collection version for tracking
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>h, PartialEq, Eq)]
struct CollectionVersion {
    collection_name: String,
    version: i32,
}

impl CollectionVersion {
    fn new(collection_name: String, version: i32) -> Self {
        Self {
            collection_name,
            version,
        }
    }
}

/// Tracks batch update offsets for collections
#[derive(Debug)]
struct BatchUpdateTracker {
    last_committed_offset: Arc<Mutex<HashMap<CollectionVersion, i64>>>,
}

impl BatchUpdateTracker {
    fn new() -> Self {
        Self {
            last_committed_offset: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    fn update_offset(&self, col_version: CollectionVersion, offset: i64) {
        let mut offsets = self.last_committed_offset.lock();
        offsets.insert(col_version, offset);
    }

    fn is_stale(
        &self,
        col_version: &CollectionVersion,
        global_offset: i64,
        batch_size: i32,
    ) -> bool {
        let offsets = self.last_committed_offset.lock();
        if let Some(&last_offset) = offsets.get(col_version) {
            global_offset - last_offset > batch_size as i64
        } else {
            false
        }
    }

    fn get_all_tracked_dbs(&self) -> Vec<CollectionVersion> {
        let offsets = self.last_committed_offset.lock();
        offsets.keys().cloned().collect()
    }
}

/// SQLite connection wrapper with shape information
#[derive(Debug)]
struct SqliteConnection {
    connection: Arc<Mutex<Connection>>,
    shape: Shape,
}

/// Main follower implementation
pub struct MainFollower {
    collection_cache: Arc<CollectionCacheManager>,
    pg_db: Arc<PostgresDatabase>,
    sqlite_base_dir: PathBuf,
    poll_interval: Duration,
    cleanup_interval: Duration,
    batch_size: i32,
    global_last_oplog_id: Arc<RwLock<i64>>,
    connections: Arc<DashMap<String, SqliteConnection>>,
    meta_db: Arc<Mutex<Connection>>,
    update_tracker: BatchUpdateTracker,
}

impl MainFollower {
    pub async fn new(
        pg_db: Arc<PostgresDatabase>,
        cache_mgr: Arc<CollectionCacheManager>,
        sqlite_base_dir: PathBuf,
        config: &FollowerConfig,
    ) -> Result<Self> {
        let poll_interval = Duration::from_secs(config.poll_interval_secs);
        let cleanup_interval = Duration::from_secs(config.cleanup_interval_secs);
        let batch_size = config.batch_size as i32;

        // Create base directory
        tokio::fs::create_dir_all(&sqlite_base_dir).await?;

        // Open meta database
        let meta_db_path = sqlite_base_dir.join("meta.sqlite");
        let meta_db = Connection::open_with_flags(
            &meta_db_path,
            OpenFlags::SQLITE_OPEN_READ_WRITE | OpenFlags::SQLITE_OPEN_CREATE,
        )?;

        init_meta_table(&meta_db)?;

        let meta_db = Arc::new(Mutex::new(meta_db));

        let follower = Self {
            collection_cache: cache_mgr,
            pg_db,
            sqlite_base_dir,
            poll_interval,
            cleanup_interval,
            batch_size,
            global_last_oplog_id: Arc::new(RwLock::new(0)),
            connections: Arc::new(DashMap::new()),
            meta_db,
            update_tracker: BatchUpdateTracker::new(),
        };

        // Initialize global last oplog ID
        follower.initialize_global_last_oplog_id().await?;

        Ok(follower)
    }

    async fn initialize_global_last_oplog_id(&self) -> Result<()> {
        let meta_db = self.meta_db.lock();
        let last_id = get_last_applied_oplog_index(&*meta_db)?;
        drop(meta_db);

        let mut global_id = self.global_last_oplog_id.write().await;
        *global_id = last_id;

        tracing::info!("Initialized global last oplog ID: {}", last_id);
        Ok(())
    }

    fn db_key(&self, collection_name: &str, version: i32) -> String {
        format!("{}_v{}", collection_name, version)
    }

    async fn get_or_create_connection(
        &self,
        collection_name: &str,
        version: i32,
    ) -> Result<Arc<Mutex<Connection>>> {
        let db_key = self.db_key(collection_name, version);

        // Check if connection already exists
        if let Some(conn_info) = self.connections.get(&db_key) {
            return Ok(conn_info.connection.clone());
        }

        // Create new connection
        let conn = self
            .create_new_db_connection(collection_name, version)
            .await?;
        let conn_arc = Arc::new(Mutex::new(conn));

        // Get collection from cache
        let collection = self
            .collection_cache
            .get_collection(collection_name)
            .await?
            .ok_or_else(|| crate::EmcacheError::CollectionNotFound(collection_name.to_string()))?;
        let shape = collection.shape;

        let sqlite_conn = SqliteConnection {
            connection: conn_arc.clone(),
            shape,
        };

        self.connections.insert(db_key, sqlite_conn);
        Ok(conn_arc)
    }

    async fn create_new_db_connection(
        &self,
        collection_name: &str,
        version: i32,
    ) -> Result<Connection> {
        // Get collection from cache
        let collection = self
            .collection_cache
            .get_collection(collection_name)
            .await?
            .ok_or_else(|| crate::EmcacheError::CollectionNotFound(collection_name.to_string()))?;
        let shape = collection.shape;

        let db_path =
            sqlite_helpers::get_collection_db_path(collection_name, &self.sqlite_base_dir, version);

        let conn = sqlite_helpers::open_collection_db(collection_name, &db_path, &shape).await?;

        // Set local DB version
        sqlite_helpers::set_local_db_version(&conn, version)?;

        tracing::info!(
            "Created new SQLite connection: collection={}, version={}, path={}",
            collection_name,
            version,
            db_path.display()
        );

        Ok(conn)
    }

    async fn apply_batch_entries(
        &self,
        conn: Arc<Mutex<Connection>>,
        entries: Vec<OplogEntry>,
        shape: &Shape,
    ) -> Result<i64> {
        let mut last_id = 0;
        let conn_guard = conn.lock();

        // Start transaction
        conn_guard.execute("BEGIN TRANSACTION", [])?;

        for entry in entries {
            sqlite_helpers::apply_oplog_entry(&*conn_guard, &entry, shape)?;
            last_id = entry.id;
        }

        // Commit transaction
        conn_guard.execute("COMMIT", [])?;
        drop(conn_guard);

        Ok(last_id)
    }

    async fn update_stale_db_offsets(&self) {
        let tracked_dbs = self.update_tracker.get_all_tracked_dbs();
        let global_id = *self.global_last_oplog_id.read().await;

        for db_key in tracked_dbs {
            if self
                .update_tracker
                .is_stale(&db_key, global_id, self.batch_size)
            {
                tracing::info!(
                    "Updating stale database offset: collection={}, version={}, to_offset={}",
                    db_key.collection_name,
                    db_key.version,
                    global_id
                );

                match self
                    .get_or_create_connection(&db_key.collection_name, db_key.version)
                    .await
                {
                    Ok(conn) => {
                        let conn_guard = conn.lock();
                        if let Err(e) =
                            sqlite_helpers::set_last_applied_oplog_index(&*conn_guard, global_id)
                        {
                            tracing::error!(
                                "Failed to update stale database offset: collection={}, version={}, error={}",
                                db_key.collection_name,
                                db_key.version,
                                e
                            );
                        }
                        drop(conn_guard);
                    }
                    Err(e) => {
                        tracing::error!(
                            "Failed to get connection for stale update: collection={}, version={}, error={}",
                            db_key.collection_name,
                            db_key.version,
                            e
                        );
                    }
                }
            }
        }
    }

    fn close_all_connections(&self) {
        tracing::info!("Closing all SQLite connections");
        self.connections.clear();
    }
}

#[async_trait]
impl FollowerInterface for MainFollower {
    async fn start(self: Arc<Self>, ctx: tokio_util::sync::CancellationToken) -> Result<()> {
        tracing::info!("MainFollower starting");

        let main_loop_ctx = ctx.clone();
        let cleanup_loop_ctx = ctx.clone();

        let main_loop_handle = {
            let follower = self.clone();
            tokio::spawn(async move {
                follower.run_main_loop(main_loop_ctx).await;
            })
        };

        let cleanup_loop_handle = {
            let follower = self.clone();
            tokio::spawn(async move {
                follower.run_cleanup_loop(cleanup_loop_ctx).await;
            })
        };

        // Wait for cancellation
        ctx.cancelled().await;

        // Wait for loops to finish
        let _ = tokio::join!(main_loop_handle, cleanup_loop_handle);

        tracing::info!("MainFollower stopped");
        self.close_all_connections();

        Ok(())
    }

    async fn ensure_open_connection(&self, collection_name: &str, version: i32) -> Result<()> {
        self.get_or_create_connection(collection_name, version)
            .await?;
        Ok(())
    }
}

impl MainFollower {
    async fn run_main_loop(&self, ctx: tokio_util::sync::CancellationToken) {
        tracing::info!("Main loop started");

        let mut polling_interval = self.poll_interval;
        let mut interval_timer = interval(polling_interval);

        loop {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Main loop stopping due to context cancellation");
                    return;
                }
                _ = interval_timer.tick() => {
                    let batch_max_processed_id = *self.global_last_oplog_id.read().await;
                    tracing::info!("Fetching oplog entries after_id={}", batch_max_processed_id);

                    match self.pg_db.get_oplog_entries_global(batch_max_processed_id, self.batch_size).await {
                        Ok(entries) => {
                            if entries.is_empty() {
                                polling_interval = self.poll_interval;
                                interval_timer = interval(polling_interval);
                                continue;
                            }

                            let mut batch_failed = false;
                            let mut batch_max_processed_id = batch_max_processed_id;
                            let mut processed_count = 0;

                            // Group entries by collection and version
                            let mut entries_by_collection: HashMap<CollectionVersion, Vec<OplogEntry>> = HashMap::new();
                            for entry in entries {
                                let col_version = CollectionVersion::new(entry.collection.clone(), entry.version);
                                entries_by_collection.entry(col_version).or_insert_with(Vec::new).push(entry);
                            }

                            // Process each collection's entries
                            for (col_version, entries) in entries_by_collection {
                                match self.get_or_create_connection(&col_version.collection_name, col_version.version).await {
                                    Ok(conn) => {
                                        // Get shape for this collection
                                        if let Some(conn_info) = self.connections.get(&self.db_key(&col_version.collection_name, col_version.version)) {
                                            let shape = conn_info.shape.clone();
                                            drop(conn_info);

                                            match self.apply_batch_entries(conn, entries.clone(), &shape).await {
                                                Ok(last_id) => {
                                                    self.update_tracker.update_offset(col_version, last_id);
                                                    batch_max_processed_id = batch_max_processed_id.max(last_id);
                                                    processed_count += entries.len();
                                                }
                                                Err(e) => {
                                                    tracing::error!(
                                                        "Failed to apply oplog entries: collection={}, version={}, error={}",
                                                        col_version.collection_name,
                                                        col_version.version,
                                                        e
                                                    );
                                                    batch_failed = true;
                                                    break;
                                                }
                                            }
                                        } else {
                                            tracing::error!("Connection info not found after creation");
                                            batch_failed = true;
                                            break;
                                        }
                                    }
                                    Err(e) => {
                                        tracing::error!(
                                            "Failed to get/create SQLite connection: collection={}, version={}, error={}",
                                            col_version.collection_name,
                                            col_version.version,
                                            e
                                        );
                                        batch_failed = true;
                                        break;
                                    }
                                }
                            }

                            // Update global offset if successful
                            if !batch_failed && batch_max_processed_id > *self.global_last_oplog_id.read().await {
                                let mut global_id = self.global_last_oplog_id.write().await;
                                *global_id = batch_max_processed_id;
                                drop(global_id);

                                let meta_db = self.meta_db.lock();
                                if let Err(e) = sqlite_helpers::set_last_applied_oplog_index(&*meta_db, batch_max_processed_id) {
                                    tracing::error!("Failed to update meta DB: {}", e);
                                }
                                drop(meta_db);

                                tracing::info!("Applied oplog entries: count={}, new_last_id={}", processed_count, batch_max_processed_id);
                            } else {
                                tracing::info!(
                                    "Applied oplog entries with no ID update: count={}, current_id={}, batch_max_id={}",
                                    processed_count,
                                    *self.global_last_oplog_id.read().await,
                                    batch_max_processed_id
                                );
                            }

                            self.update_stale_db_offsets().await;

                            // Adjust polling interval based on batch size
                            if processed_count == self.batch_size as usize {
                                polling_interval = Duration::from_millis(1);
                            } else {
                                polling_interval = self.poll_interval;
                            }
                            interval_timer = interval(polling_interval);
                        }
                        Err(e) => {
                            tracing::error!("Failed to fetch oplog entries: after_id={}, error={}", batch_max_processed_id, e);
                        }
                    }
                }
            }
        }
    }

    async fn run_cleanup_loop(&self, ctx: tokio_util::sync::CancellationToken) {
        tracing::info!("Cleanup loop started");

        let mut cleanup_interval = interval(self.cleanup_interval);

        loop {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Cleanup loop stopping due to context cancellation");
                    return;
                }
                _ = cleanup_interval.tick() => {
                    tracing::debug!("Running cleanup tasks");
                    // TODO: Implement cleanup logic (e.g., removing old SQLite files)
                }
            }
        }
    }
}
