use rusqlite::{params, Connection, OpenFlags};
use std::path::{Path, PathBuf};

use crate::{
    db::OplogEntry,
    shape::{DataType, Shape},
    Result,
};

// Constants
const METADATA_TABLE_NAME: &str = "metadata";
const LAST_APPLIED_IDX_KEY: &str = "last_applied_oplog_idx";
const DB_VERSION_KEY: &str = "db_version";
const DATA_TABLE_NAME: &str = "data";

/// Get the path for a collection's SQLite database
pub fn get_collection_db_path(
    collection_name: &str,
    sqlite_base_dir: &Path,
    version: i32,
) -> PathBuf {
    let coll_dir = sqlite_base_dir.join("replicas");
    let dir_name = format!("{}_v{}", collection_name, version);
    coll_dir.join(dir_name).join("db.sqlite")
}

/// Open a collection database with proper initialization
pub async fn open_collection_db(
    _collection_name: &str,
    db_path: &Path,
    coll_shape: &Shape,
) -> Result<Connection> {
    // Create directory if it doesn't exist
    if let Some(parent) = db_path.parent() {
        tokio::fs::create_dir_all(parent).await?;
    }

    let conn = Connection::open_with_flags(
        db_path,
        OpenFlags::SQLITE_OPEN_READ_WRITE | OpenFlags::SQLITE_OPEN_CREATE,
    )?;

    // Enable WAL mode for better concurrency
    conn.execute("PRAGMA journal_mode=WAL", [])?;

    init_meta_table(&conn)?;
    ensure_collection_table_and_indexes(&conn, coll_shape)?;

    Ok(conn)
}

/// Initialize the metadata table
pub fn init_meta_table(conn: &Connection) -> Result<()> {
    let sql = format!(
        "CREATE TABLE IF NOT EXISTS {} (key TEXT PRIMARY KEY, value TEXT)",
        METADATA_TABLE_NAME
    );
    conn.execute(&sql, [])?;
    Ok(())
}

/// Get the last applied oplog index from metadata
pub fn get_last_applied_oplog_index(conn: &Connection) -> Result<i64> {
    let query = format!("SELECT value FROM {} WHERE key = ?", METADATA_TABLE_NAME);

    let mut stmt = conn.prepare(&query)?;
    let result = stmt.query_row([LAST_APPLIED_IDX_KEY], |row| {
        let value_str: String = row.get(0)?;
        Ok(value_str.parse::<i64>().unwrap_or(0))
    });

    match result {
        Ok(idx) => Ok(idx),
        Err(rusqlite::Error::QueryReturnedNoRows) => Ok(0),
        Err(e) => Err(e.into()),
    }
}

/// Set the last applied oplog index in metadata
pub fn set_last_applied_oplog_index(conn: &Connection, index: i64) -> Result<()> {
    let sql = format!(
        "INSERT INTO {} (key, value) VALUES (?, ?) ON CONFLICT(key) DO UPDATE SET value = excluded.value",
        METADATA_TABLE_NAME
    );
    conn.execute(&sql, params![LAST_APPLIED_IDX_KEY, index.to_string()])?;
    Ok(())
}

/// Set the local database version
pub fn set_local_db_version(conn: &Connection, version: i32) -> Result<()> {
    let sql = format!(
        "INSERT INTO {} (key, value) VALUES (?, ?) ON CONFLICT(key) DO UPDATE SET value = excluded.value",
        METADATA_TABLE_NAME
    );
    conn.execute(&sql, params![DB_VERSION_KEY, version.to_string()])?;
    Ok(())
}

/// Map shape data type to SQLite type
fn map_shape_type_to_sqlite(dt: &DataType) -> &'static str {
    match dt {
        DataType::Any => "BLOB",
        DataType::Bool => "INTEGER",
        DataType::Number => "REAL",
        DataType::Integer => "INTEGER",
        DataType::Text => "TEXT",
        DataType::Jsonb => "BLOB",
    }
}

/// Quote SQL identifier
fn quote_identifier(ident: &str) -> String {
    format!("\"{}\"", ident)
}

/// Ensure collection table and indexes exist
pub fn ensure_collection_table_and_indexes(conn: &Connection, coll_shape: &Shape) -> Result<()> {
    let mut col_defs = Vec::new();
    let id_col_def = format!("{} TEXT PRIMARY KEY", quote_identifier("id"));
    col_defs.push(id_col_def);

    let mut col_names = std::collections::HashSet::new();
    col_names.insert("id".to_string());

    for col in &coll_shape.columns {
        if col.name == "id" {
            return Err(crate::EmcacheError::Internal(
                "Column name 'id' is reserved and implicitly created".to_string(),
            ));
        }
        if col_names.contains(&col.name) {
            return Err(crate::EmcacheError::Internal(format!(
                "Duplicate column name defined in shape: {}",
                col.name
            )));
        }
        let col_def = format!(
            "{} {}",
            quote_identifier(&col.name),
            map_shape_type_to_sqlite(&col.data_type)
        );
        col_defs.push(col_def);
        col_names.insert(col.name.clone());
    }

    let table_sql = format!(
        "CREATE TABLE IF NOT EXISTS {} ({})",
        quote_identifier(DATA_TABLE_NAME),
        col_defs.join(", ")
    );
    conn.execute(&table_sql, [])?;

    // Create indexes
    for (idx, index) in coll_shape.indexes.iter().enumerate() {
        let index_name = format!("idx_{}_{}", DATA_TABLE_NAME, idx);
        let columns: Vec<String> = index.columns.iter().map(|c| quote_identifier(c)).collect();
        let index_sql = format!(
            "CREATE INDEX IF NOT EXISTS {} ON {} ({})",
            quote_identifier(&index_name),
            quote_identifier(DATA_TABLE_NAME),
            columns.join(", ")
        );
        conn.execute(&index_sql, [])?;
    }

    Ok(())
}

/// Apply an oplog entry to the SQLite database
pub fn apply_oplog_entry(conn: &Connection, entry: &OplogEntry, coll_shape: &Shape) -> Result<()> {
    match entry.operation.as_str() {
        "UPSERT" => {
            if let Some(ref doc) = entry.doc {
                upsert_document(conn, &entry.doc_id, doc, coll_shape)?;
            } else {
                tracing::warn!(
                    "UPSERT operation with nil document: collection={}, doc_id={}",
                    entry.collection,
                    entry.doc_id
                );
            }
        }
        "DELETE" => {
            delete_document(conn, &entry.doc_id)?;
        }
        _ => {
            return Err(crate::EmcacheError::Internal(format!(
                "Unknown oplog operation: {}",
                entry.operation
            )));
        }
    }
    Ok(())
}

/// Upsert a document into the data table
fn upsert_document(
    conn: &Connection,
    doc_id: &str,
    doc: &serde_json::Value,
    coll_shape: &Shape,
) -> Result<()> {
    let mut columns = vec!["id".to_string()];
    let mut placeholders = vec!["?".to_string()];
    let mut values: Vec<Box<dyn rusqlite::ToSql>> = vec![Box::new(doc_id.to_string())];

    for col in &coll_shape.columns {
        columns.push(quote_identifier(&col.name));
        placeholders.push("?".to_string());

        let value = extract_value_from_document(doc, &col.path, &col.data_type)?;
        values.push(value);
    }

    let update_assignments: Vec<String> = columns[1..]
        .iter()
        .map(|col| format!("{} = excluded.{}", col, col))
        .collect();

    let sql = format!(
        "INSERT INTO {} ({}) VALUES ({}) ON CONFLICT(id) DO UPDATE SET {}",
        quote_identifier(DATA_TABLE_NAME),
        columns.join(", "),
        placeholders.join(", "),
        update_assignments.join(", ")
    );

    let mut stmt = conn.prepare(&sql)?;
    let params: Vec<&dyn rusqlite::ToSql> = values.iter().map(|v| v.as_ref()).collect();
    stmt.execute(&params[..])?;

    Ok(())
}

/// Delete a document from the data table
fn delete_document(conn: &Connection, doc_id: &str) -> Result<()> {
    let sql = format!(
        "DELETE FROM {} WHERE id = ?",
        quote_identifier(DATA_TABLE_NAME)
    );
    conn.execute(&sql, params![doc_id])?;
    Ok(())
}

/// Extract value from document based on path and data type
fn extract_value_from_document(
    doc: &serde_json::Value,
    path: &str,
    data_type: &DataType,
) -> Result<Box<dyn rusqlite::ToSql>> {
    let value = if path.contains('.') {
        // Handle nested paths
        let parts: Vec<&str> = path.split('.').collect();
        let mut current = doc;
        for part in parts {
            current = current.get(part).unwrap_or(&serde_json::Value::Null);
        }
        current
    } else {
        doc.get(path).unwrap_or(&serde_json::Value::Null)
    };

    match data_type {
        DataType::Text => {
            if let Some(s) = value.as_str() {
                Ok(Box::new(s.to_string()))
            } else {
                Ok(Box::new(None::<String>))
            }
        }
        DataType::Integer => {
            if let Some(i) = value.as_i64() {
                Ok(Box::new(i))
            } else {
                Ok(Box::new(None::<i64>))
            }
        }
        DataType::Number => {
            if let Some(f) = value.as_f64() {
                Ok(Box::new(f))
            } else {
                Ok(Box::new(None::<f64>))
            }
        }
        DataType::Bool => {
            if let Some(b) = value.as_bool() {
                Ok(Box::new(if b { 1i64 } else { 0i64 }))
            } else {
                Ok(Box::new(None::<i64>))
            }
        }
        DataType::Jsonb | DataType::Any => {
            if value.is_null() {
                Ok(Box::new(None::<Vec<u8>>))
            } else {
                let json_str = serde_json::to_string(value)?;
                Ok(Box::new(json_str.into_bytes()))
            }
        }
    }
}
