use thiserror::Error;

pub type Result<T> = std::result::Result<T, EmcacheError>;

#[derive(Error, Debug)]
pub enum EmcacheError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("MongoDB error: {0}")]
    MongoDB(#[from] mongodb::error::Error),

    #[error("SQLite error: {0}")]
    Sqlite(#[from] rusqlite::Error),

    #[error("Task join error: {0}")]
    TaskJoin(#[from] tokio::task::JoinError),

    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("gRPC error: {0}")]
    Grpc(#[from] tonic::Status),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Collection not found: {0}")]
    CollectionNotFound(String),

    #[error("Collection already exists: {0}")]
    CollectionAlreadyExists(String),

    #[error("Leader election failed: {0}")]
    LeaderElection(String),

    #[error("Compression error: {0}")]
    Compression(String),

    #[error("Internal error: {0}")]
    Internal(String),
}

impl From<EmcacheError> for tonic::Status {
    fn from(err: EmcacheError) -> Self {
        match err {
            EmcacheError::CollectionNotFound(msg) => {
                tonic::Status::not_found(format!("Collection not found: {}", msg))
            }
            EmcacheError::CollectionAlreadyExists(msg) => {
                tonic::Status::already_exists(format!("Collection already exists: {}", msg))
            }
            EmcacheError::Validation(err) => {
                tonic::Status::invalid_argument(format!("Validation error: {}", err))
            }
            EmcacheError::Grpc(status) => status,
            _ => tonic::Status::internal(format!("Internal error: {}", err)),
        }
    }
}
