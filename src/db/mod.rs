use async_trait::async_trait;
use bson;
use chrono::{DateTime, Utc};
// Type alias for MongoDB client to avoid import issues
pub type MongoClient = mongodb::Client;
use serde::{Deserialize, Serialize};
use sqlx::{PgPool, Row};

use crate::{shape::Shape, Result};

// MongoDB integration will be implemented later

// Data structures
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OplogEntry {
    pub id: i64,
    pub operation: String, // "UPSERT" or "DELETE"
    pub doc_id: String,
    pub created_at: DateTime<Utc>,
    pub collection: String,
    pub doc: Option<serde_json::Value>, // JSON data
    pub version: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplicatedCollection {
    pub collection_name: String,
    pub current_version: i32,
    pub shape: Shape,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CollectionVersion {
    pub collection_name: String,
    pub version: i32,
}

// Repository traits
#[async_trait]
pub trait OplogRepository {
    async fn get_oplog_entries_multiple_collections(
        &self,
        collections: &[String],
        after_id: i64,
        limit: i32,
    ) -> Result<Vec<OplogEntry>>;

    async fn get_oplog_entries_global(&self, after_id: i64, limit: i32) -> Result<Vec<OplogEntry>>;

    async fn insert_oplog_entry(&self, entry: OplogEntry) -> Result<i64>;
}

#[async_trait]
pub trait CollectionRepository {
    async fn add_replicated_collection(&self, name: &str, shape: &Shape) -> Result<()>;

    async fn remove_replicated_collection(&self, name: &str) -> Result<()>;

    async fn get_replicated_collection(&self, name: &str) -> Result<Option<ReplicatedCollection>>;

    async fn list_replicated_collections(&self) -> Result<Vec<String>>;

    async fn get_all_current_collection_versions(&self) -> Result<Vec<CollectionVersion>>;

    async fn update_collection_shape(
        &self,
        name: &str,
        new_shape: &Shape,
        new_version: i32,
    ) -> Result<()>;

    async fn get_all_replicated_collections_with_shapes(&self)
        -> Result<Vec<ReplicatedCollection>>;

    async fn increment_collection_version(&self, collection_name: &str) -> Result<i32>;
}

#[async_trait]
pub trait ResumeTokenRepository {
    async fn get_resume_token(&self, collection: &str) -> Result<Option<String>>;
    async fn upsert_resume_token(&self, collection: &str, token: &str) -> Result<()>;
}

// Combined database trait
#[async_trait]
pub trait Database: OplogRepository + CollectionRepository + ResumeTokenRepository {
    async fn connect_postgres(database_url: &str) -> Result<Self>
    where
        Self: Sized;
    async fn connect_mongo(mongo_url: &str) -> Result<MongoClient>;
}

// PostgreSQL implementation
pub struct PostgresDatabase {
    pool: PgPool,
}

impl PostgresDatabase {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn connect(database_url: &str) -> Result<Self> {
        let pool = PgPool::connect(database_url).await?;

        // Test the connection
        sqlx::query("SELECT 1").execute(&pool).await?;

        tracing::info!("Database connection established: PostgreSQL");
        Ok(Self::new(pool))
    }

    pub async fn connect_mongo(mongo_url: &str) -> Result<MongoClient> {
        let client = MongoClient::with_uri_str(mongo_url).await?;

        // Test the connection
        client
            .database("admin")
            .run_command(bson::doc! {"ping": 1})
            .await?;

        tracing::info!("Database connection established: MongoDB");
        Ok(client)
    }
}

#[async_trait]
impl OplogRepository for PostgresDatabase {
    async fn get_oplog_entries_multiple_collections(
        &self,
        collections: &[String],
        after_id: i64,
        limit: i32,
    ) -> Result<Vec<OplogEntry>> {
        if collections.is_empty() {
            return Ok(vec![]);
        }

        let sql = r#"
            SELECT id, operation, doc_id, created_at, collection, doc, version
            FROM oplog
            WHERE collection = ANY($1) AND id > $2
            ORDER BY id ASC
            LIMIT $3
        "#;

        let rows = sqlx::query(sql)
            .bind(collections)
            .bind(after_id)
            .bind(limit)
            .fetch_all(&self.pool)
            .await?;

        let mut entries = Vec::new();
        for row in rows {
            let doc: Option<serde_json::Value> = if row.get::<&str, _>("operation") == "UPSERT" {
                row.get("doc")
            } else {
                None
            };

            entries.push(OplogEntry {
                id: row.get("id"),
                operation: row.get("operation"),
                doc_id: row.get("doc_id"),
                created_at: row.get("created_at"),
                collection: row.get("collection"),
                doc,
                version: row.get("version"),
            });
        }

        Ok(entries)
    }

    async fn get_oplog_entries_global(&self, after_id: i64, limit: i32) -> Result<Vec<OplogEntry>> {
        let sql = r#"
            SELECT id, operation, doc_id, created_at, collection, doc, version
            FROM oplog
            WHERE id > $1
            ORDER BY id ASC
            LIMIT $2
        "#;

        let rows = sqlx::query(sql)
            .bind(after_id)
            .bind(limit)
            .fetch_all(&self.pool)
            .await?;

        let mut entries = Vec::new();
        for row in rows {
            let doc: Option<serde_json::Value> = if row.get::<&str, _>("operation") == "UPSERT" {
                row.get("doc")
            } else {
                None
            };

            entries.push(OplogEntry {
                id: row.get("id"),
                operation: row.get("operation"),
                doc_id: row.get("doc_id"),
                created_at: row.get("created_at"),
                collection: row.get("collection"),
                doc,
                version: row.get("version"),
            });
        }

        Ok(entries)
    }

    async fn insert_oplog_entry(&self, entry: OplogEntry) -> Result<i64> {
        let sql = r#"
            INSERT INTO oplog (operation, doc_id, created_at, collection, doc, version)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id
        "#;

        let row = sqlx::query(sql)
            .bind(&entry.operation)
            .bind(&entry.doc_id)
            .bind(entry.created_at)
            .bind(&entry.collection)
            .bind(&entry.doc)
            .bind(entry.version)
            .fetch_one(&self.pool)
            .await?;

        Ok(row.get("id"))
    }
}

#[async_trait]
impl CollectionRepository for PostgresDatabase {
    async fn add_replicated_collection(&self, name: &str, shape: &Shape) -> Result<()> {
        let shape_json = serde_json::to_value(shape)?;
        let sql = "INSERT INTO replicated_collections (collection_name, shape) VALUES ($1, $2)";

        match sqlx::query(sql)
            .bind(name)
            .bind(shape_json)
            .execute(&self.pool)
            .await
        {
            Ok(_) => {
                tracing::info!("Collection added to replication: {}", name);
                Ok(())
            }
            Err(sqlx::Error::Database(db_err))
                if db_err.constraint() == Some("replicated_collections_pkey") =>
            {
                tracing::warn!("Collection already exists: {}", name);
                Err(crate::EmcacheError::CollectionAlreadyExists(
                    name.to_string(),
                ))
            }
            Err(e) => Err(e.into()),
        }
    }

    async fn remove_replicated_collection(&self, name: &str) -> Result<()> {
        let sql = "DELETE FROM replicated_collections WHERE collection_name = $1";
        let result = sqlx::query(sql).bind(name).execute(&self.pool).await?;

        if result.rows_affected() == 0 {
            tracing::warn!("Collection not found for removal: {}", name);
            return Err(crate::EmcacheError::CollectionNotFound(name.to_string()));
        }

        tracing::info!("Collection removed from replication: {}", name);
        Ok(())
    }

    async fn get_replicated_collection(&self, name: &str) -> Result<Option<ReplicatedCollection>> {
        let sql = r#"
            SELECT collection_name, current_version, shape
            FROM replicated_collections
            WHERE collection_name = $1
        "#;

        let row = sqlx::query(sql)
            .bind(name)
            .fetch_optional(&self.pool)
            .await?;

        if let Some(row) = row {
            let shape_json: serde_json::Value = row.get("shape");
            let shape: Shape = serde_json::from_value(shape_json)?;

            Ok(Some(ReplicatedCollection {
                collection_name: row.get("collection_name"),
                current_version: row.get("current_version"),
                shape,
            }))
        } else {
            Ok(None)
        }
    }

    async fn list_replicated_collections(&self) -> Result<Vec<String>> {
        let sql = "SELECT collection_name FROM replicated_collections ORDER BY collection_name";
        let rows = sqlx::query(sql).fetch_all(&self.pool).await?;

        Ok(rows
            .into_iter()
            .map(|row| row.get("collection_name"))
            .collect())
    }

    async fn get_all_current_collection_versions(&self) -> Result<Vec<CollectionVersion>> {
        let sql = "SELECT collection_name, current_version FROM replicated_collections";
        let rows = sqlx::query(sql).fetch_all(&self.pool).await?;

        Ok(rows
            .into_iter()
            .map(|row| CollectionVersion {
                collection_name: row.get("collection_name"),
                version: row.get("current_version"),
            })
            .collect())
    }

    async fn update_collection_shape(
        &self,
        name: &str,
        new_shape: &Shape,
        new_version: i32,
    ) -> Result<()> {
        let shape_json = serde_json::to_value(new_shape)?;
        let sql = r#"
            UPDATE replicated_collections
            SET shape = $1, current_version = $2
            WHERE collection_name = $3
        "#;

        sqlx::query(sql)
            .bind(shape_json)
            .bind(new_version)
            .bind(name)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    async fn get_all_replicated_collections_with_shapes(
        &self,
    ) -> Result<Vec<ReplicatedCollection>> {
        let sql = r#"
            SELECT collection_name, current_version, shape
            FROM replicated_collections
            ORDER BY collection_name
        "#;

        let rows = sqlx::query(sql).fetch_all(&self.pool).await?;

        let mut collections = Vec::new();
        for row in rows {
            let shape_json: serde_json::Value = row.get("shape");

            // Skip collections with invalid shapes (similar to Go implementation)
            match serde_json::from_value::<Shape>(shape_json) {
                Ok(shape) => {
                    collections.push(ReplicatedCollection {
                        collection_name: row.get("collection_name"),
                        current_version: row.get("current_version"),
                        shape,
                    });
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to unmarshal shape JSON for collection {}: {}",
                        row.get::<&str, _>("collection_name"),
                        e
                    );
                    continue;
                }
            }
        }

        Ok(collections)
    }

    async fn increment_collection_version(&self, collection_name: &str) -> Result<i32> {
        let sql = r#"
            UPDATE replicated_collections
            SET current_version = current_version + 1
            WHERE collection_name = $1
            RETURNING current_version
        "#;

        let row = sqlx::query(sql)
            .bind(collection_name)
            .fetch_one(&self.pool)
            .await?;

        let new_version: i32 = row.get("current_version");
        tracing::info!(
            "Collection version incremented: {} -> {}",
            collection_name,
            new_version
        );

        Ok(new_version)
    }
}

#[async_trait]
impl ResumeTokenRepository for PostgresDatabase {
    async fn get_resume_token(&self, collection: &str) -> Result<Option<String>> {
        let sql = "SELECT token FROM resume_tokens WHERE collection = $1";

        let row = sqlx::query(sql)
            .bind(collection)
            .fetch_optional(&self.pool)
            .await?;

        Ok(row.map(|r| r.get("token")))
    }

    async fn upsert_resume_token(&self, collection: &str, token: &str) -> Result<()> {
        let sql = r#"
            INSERT INTO resume_tokens (collection, token, updated_at)
            VALUES ($1, $2, NOW())
            ON CONFLICT (collection)
            DO UPDATE SET token = EXCLUDED.token, updated_at = NOW()
        "#;

        sqlx::query(sql)
            .bind(collection)
            .bind(token)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

#[async_trait]
impl Database for PostgresDatabase {
    async fn connect_postgres(database_url: &str) -> Result<Self> {
        Self::connect(database_url).await
    }

    async fn connect_mongo(mongo_url: &str) -> Result<MongoClient> {
        Self::connect_mongo(mongo_url).await
    }
}
