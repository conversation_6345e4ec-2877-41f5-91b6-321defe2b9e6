use serde::{Deserialize, Serialize};
use validator::<PERSON>ida<PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum DataType {
    #[serde(rename = "jsonb")]
    Jsonb,
    #[serde(rename = "any")]
    Any,
    #[serde(rename = "bool")]
    Bo<PERSON>,
    #[serde(rename = "number")]
    Number,
    #[serde(rename = "integer")]
    Integer,
    #[serde(rename = "text")]
    Text,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Column {
    #[validate(length(min = 1, max = 255))]
    pub name: String,
    #[serde(rename = "type")]
    pub data_type: DataType,
    #[validate(length(min = 1, max = 255))]
    pub path: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct Index {
    #[validate(length(min = 1))]
    pub columns: Vec<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, <PERSON>ida<PERSON>)]
pub struct Filter {
    #[validate(length(min = 1, max = 255))]
    pub path: String,
    #[validate(length(min = 1, max = 255))]
    pub value: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct Shape {
    #[validate(length(min = 1))]
    #[validate(nested)]
    pub columns: Vec<Column>,
    #[validate(nested)]
    pub indexes: Vec<Index>,
    #[serde(default)]
    #[validate(nested)]
    pub filters: Vec<Filter>,
}
