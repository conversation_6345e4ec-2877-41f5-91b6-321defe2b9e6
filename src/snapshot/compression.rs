use flate2::{read::GzDecoder, write::Gz<PERSON>ncoder, Compression as GzCompression};
use std::io::{Read, Write};
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use tokio_stream::Stream;
use tonic::Status;
use zstd::{Decoder as ZstdDecoder, Encoder as ZstdEncoder};

use crate::proto::{Compression, DownloadDbResponse};

/// Compression utilities for streaming data
pub struct CompressionUtils;

impl CompressionUtils {
    /// Compress and stream data using the specified compression algorithm
    pub async fn compress_and_stream<R, S>(
        reader: R,
        compression: Compression,
        chunk_size: usize,
    ) -> Result<impl Stream<Item = Result<DownloadDbResponse, Status>>, Status>
    where
        R: AsyncRead + Unpin + Send + 'static,
        S: futures::Sink<DownloadDbResponse> + Unpin,
    {
        let (tx, rx) = tokio::sync::mpsc::channel(4);

        match compression {
            Compression::None => {
                tokio::spawn(async move {
                    if let Err(e) = Self::stream_uncompressed(reader, tx, chunk_size).await {
                        tracing::error!("Failed to stream uncompressed data: {}", e);
                    }
                });
            }
            Compression::Gzip => {
                tokio::spawn(async move {
                    if let Err(e) = Self::stream_gzip_compressed(reader, tx, chunk_size).await {
                        tracing::error!("Failed to stream gzip compressed data: {}", e);
                    }
                });
            }
            Compression::Zstd => {
                tokio::spawn(async move {
                    if let Err(e) = Self::stream_zstd_compressed(reader, tx, chunk_size).await {
                        tracing::error!("Failed to stream zstd compressed data: {}", e);
                    }
                });
            }
        }

        Ok(tokio_stream::wrappers::ReceiverStream::new(rx))
    }

    /// Stream uncompressed data
    async fn stream_uncompressed<R>(
        mut reader: R,
        tx: tokio::sync::mpsc::Sender<Result<DownloadDbResponse, Status>>,
        chunk_size: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        R: AsyncRead + Unpin,
    {
        let mut buffer = vec![0u8; chunk_size];
        let mut total_sent = 0u64;

        loop {
            let bytes_read = reader.read(&mut buffer).await?;
            if bytes_read == 0 {
                break; // EOF
            }

            total_sent += bytes_read as u64;
            let chunk = buffer[..bytes_read].to_vec();
            let response = DownloadDbResponse {
                version: 1, // TODO: Use actual version
                chunk,
                compression: Compression::None as i32,
            };

            if tx.send(Ok(response)).await.is_err() {
                break; // Receiver dropped
            }
        }

        tracing::info!("Streamed uncompressed data: {} bytes", total_sent);
        Ok(())
    }

    /// Stream gzip compressed data
    async fn stream_gzip_compressed<R>(
        reader: R,
        tx: tokio::sync::mpsc::Sender<Result<DownloadDbResponse, Status>>,
        chunk_size: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        R: AsyncRead + Unpin + Send + 'static,
    {
        let (pipe_reader, mut pipe_writer) = tokio::io::duplex(chunk_size * 4);

        // Spawn compression task
        let compress_handle = tokio::spawn(async move {
            let mut sync_reader = tokio_util::io::SyncIoBridge::new(reader);
            let mut encoder = GzEncoder::new(Vec::new(), GzCompression::default());

            let mut buffer = vec![0u8; chunk_size];
            loop {
                let bytes_read = sync_reader.read(&mut buffer)?;
                if bytes_read == 0 {
                    break;
                }
                encoder.write_all(&buffer[..bytes_read])?;
            }

            let compressed_data = encoder.finish()?;
            pipe_writer.write_all(&compressed_data).await?;
            pipe_writer.shutdown().await?;

            Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
        });

        // Stream compressed data
        let stream_handle = tokio::spawn(async move {
            Self::stream_uncompressed_with_compression_type(
                pipe_reader,
                tx,
                chunk_size,
                Compression::Gzip,
            )
            .await
        });

        // Wait for both tasks
        let (compress_result, stream_result) = tokio::join!(compress_handle, stream_handle);
        compress_result??;
        stream_result??;

        Ok(())
    }

    /// Stream zstd compressed data
    async fn stream_zstd_compressed<R>(
        reader: R,
        tx: tokio::sync::mpsc::Sender<Result<DownloadDbResponse, Status>>,
        chunk_size: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        R: AsyncRead + Unpin + Send + 'static,
    {
        let (pipe_reader, mut pipe_writer) = tokio::io::duplex(chunk_size * 4);

        // Spawn compression task
        let compress_handle = tokio::spawn(async move {
            let mut sync_reader = tokio_util::io::SyncIoBridge::new(reader);
            let mut encoder = ZstdEncoder::new(Vec::new(), 3)?; // Compression level 3

            let mut buffer = vec![0u8; chunk_size];
            loop {
                let bytes_read = sync_reader.read(&mut buffer)?;
                if bytes_read == 0 {
                    break;
                }
                encoder.write_all(&buffer[..bytes_read])?;
            }

            let compressed_data = encoder.finish()?;
            pipe_writer.write_all(&compressed_data).await?;
            pipe_writer.shutdown().await?;

            Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
        });

        // Stream compressed data
        let stream_handle = tokio::spawn(async move {
            Self::stream_uncompressed_with_compression_type(
                pipe_reader,
                tx,
                chunk_size,
                Compression::Zstd,
            )
            .await
        });

        // Wait for both tasks
        let (compress_result, stream_result) = tokio::join!(compress_handle, stream_handle);
        compress_result??;
        stream_result??;

        Ok(())
    }

    /// Helper to stream data with a specific compression type marker
    async fn stream_uncompressed_with_compression_type<R>(
        mut reader: R,
        tx: tokio::sync::mpsc::Sender<Result<DownloadDbResponse, Status>>,
        chunk_size: usize,
        compression: Compression,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        R: AsyncRead + Unpin,
    {
        let mut buffer = vec![0u8; chunk_size];
        let mut total_sent = 0u64;

        loop {
            let bytes_read = reader.read(&mut buffer).await?;
            if bytes_read == 0 {
                break; // EOF
            }

            total_sent += bytes_read as u64;
            let chunk = buffer[..bytes_read].to_vec();
            let response = DownloadDbResponse {
                version: 1, // TODO: Use actual version
                chunk,
                compression: compression as i32,
            };

            if tx.send(Ok(response)).await.is_err() {
                break; // Receiver dropped
            }
        }

        tracing::info!(
            "Streamed compressed data: {} bytes, compression: {:?}",
            total_sent,
            compression
        );
        Ok(())
    }
}

/// Decompression utilities for client-side data processing
pub struct DecompressionUtils;

impl DecompressionUtils {
    /// Decompress a stream of data based on compression type
    pub async fn decompress_stream<W>(
        mut chunks: impl Stream<Item = Result<Vec<u8>, Status>> + Unpin,
        compression: Compression,
        mut writer: W,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        W: AsyncWrite + Unpin,
    {
        match compression {
            Compression::None => {
                while let Some(chunk_result) = futures::StreamExt::next(&mut chunks).await {
                    let chunk = chunk_result.map_err(|e| format!("Stream error: {}", e))?;
                    writer.write_all(&chunk).await?;
                }
                writer.flush().await?;
            }
            Compression::Gzip => {
                Self::decompress_gzip_stream(chunks, &mut writer).await?;
                writer.flush().await?;
            }
            Compression::Zstd => {
                Self::decompress_zstd_stream(chunks, &mut writer).await?;
                writer.flush().await?;
            }
        }

        Ok(())
    }

    /// Decompress gzip stream
    async fn decompress_gzip_stream<W>(
        mut chunks: impl Stream<Item = Result<Vec<u8>, Status>> + Unpin,
        writer: &mut W,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        W: AsyncWrite + Unpin,
    {
        let mut compressed_data = Vec::new();

        // Collect all chunks first
        while let Some(chunk_result) = futures::StreamExt::next(&mut chunks).await {
            let chunk = chunk_result.map_err(|e| format!("Stream error: {}", e))?;
            compressed_data.extend_from_slice(&chunk);
        }

        // Decompress all at once
        let mut decoder = GzDecoder::new(&compressed_data[..]);
        let mut decompressed = Vec::new();
        decoder.read_to_end(&mut decompressed)?;

        writer.write_all(&decompressed).await?;
        Ok(())
    }

    /// Decompress zstd stream
    async fn decompress_zstd_stream<W>(
        mut chunks: impl Stream<Item = Result<Vec<u8>, Status>> + Unpin,
        writer: &mut W,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>>
    where
        W: AsyncWrite + Unpin,
    {
        let mut compressed_data = Vec::new();

        // Collect all chunks first
        while let Some(chunk_result) = futures::StreamExt::next(&mut chunks).await {
            let chunk = chunk_result.map_err(|e| format!("Stream error: {}", e))?;
            compressed_data.extend_from_slice(&chunk);
        }

        // Decompress all at once
        let mut decoder = ZstdDecoder::new(&compressed_data[..])?;
        let mut decompressed = Vec::new();
        decoder.read_to_end(&mut decompressed)?;

        writer.write_all(&decompressed).await?;
        Ok(())
    }
}
