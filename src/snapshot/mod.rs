use async_trait::async_trait;

use parking_lot::Mutex;
use rusqlite::{backup::Backup, Connection, OpenFlags};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
    time::{Duration, SystemTime},
};
use tokio::{fs, time::interval};
use tokio_util::sync::CancellationToken;

use crate::Result;

pub mod compression;

const SNAPSHOT_DIR_NAME: &str = "snapshots";

/// Information about a snapshot including reference counting and creation time
#[derive(Debug, Clone)]
struct SnapshotInfo {
    ref_count: i32,
    creation_time: SystemTime,
}

impl SnapshotInfo {
    fn new() -> Self {
        Self {
            ref_count: 1,
            creation_time: SystemTime::now(),
        }
    }
}

/// Global snapshot manager with reference counting and cleanup
static SNAPSHOT_MANAGER: once_cell::sync::Lazy<Arc<SnapshotManagerImpl>> =
    once_cell::sync::Lazy::new(|| Arc::new(SnapshotManagerImpl::new()));

/// Internal snapshot manager implementation
struct SnapshotManagerImpl {
    snapshots: Arc<Mutex<HashMap<String, SnapshotInfo>>>,
}

impl SnapshotManagerImpl {
    fn new() -> Self {
        Self {
            snapshots: Arc::new(Mutex::new(HashMap::new())),
        }
    }
}

/// Interface for snapshot management
#[async_trait]
pub trait SnapshotManagerInterface {
    async fn get_or_generate_snapshot(
        &self,
        db_path: &Path,
        base_dir: &Path,
        snapshot_filename: &str,
    ) -> Result<(PathBuf, Box<dyn Fn() + Send + Sync>)>;

    async fn start_cleanup_loop(
        &self,
        ctx: CancellationToken,
        ttl: Duration,
        sqlite_base_dir: &Path,
    ) -> Result<()>;
}

/// Main snapshot manager
pub struct SnapshotManager {
    inner: Arc<SnapshotManagerImpl>,
}

impl SnapshotManager {
    pub fn new() -> Self {
        Self {
            inner: SNAPSHOT_MANAGER.clone(),
        }
    }
}

impl Default for SnapshotManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl SnapshotManagerInterface for SnapshotManager {
    async fn get_or_generate_snapshot(
        &self,
        db_path: &Path,
        base_dir: &Path,
        snapshot_filename: &str,
    ) -> Result<(PathBuf, Box<dyn Fn() + Send + Sync>)> {
        let snapshot_path = base_dir.join(SNAPSHOT_DIR_NAME).join(snapshot_filename);
        let snapshot_path_str = snapshot_path.to_string_lossy().to_string();

        // Create cleanup function
        let snapshots_ref = self.inner.snapshots.clone();
        let cleanup_path = snapshot_path_str.clone();
        let cleanup_func = Box::new(move || {
            let mut snapshots = snapshots_ref.lock();
            if let Some(info) = snapshots.get_mut(&cleanup_path) {
                info.ref_count -= 1;
                tracing::info!(
                    "Decremented ref count upon download completion: path={}, ref_count={}",
                    cleanup_path,
                    info.ref_count
                );
            } else {
                tracing::info!(
                    "Snapshot was already cleaned up or replaced before download completion: path={}",
                    cleanup_path
                );
            }
        });

        // Check if snapshot already exists (scope the lock)
        {
            let mut snapshots = self.inner.snapshots.lock();
            if let Some(info) = snapshots.get_mut(&snapshot_path_str) {
                info.ref_count += 1;
                tracing::info!(
                    "Reusing existing snapshot: path={}, ref_count={}",
                    snapshot_path_str,
                    info.ref_count
                );
                return Ok((snapshot_path, cleanup_func));
            }
        } // Lock is dropped here

        tracing::info!(
            "Creating new snapshot: path={}, source={}",
            snapshot_path.display(),
            db_path.display()
        );

        // Check if source database exists
        if !db_path.exists() {
            return Err(crate::EmcacheError::Internal(format!(
                "Source database file {} does not exist",
                db_path.display()
            )));
        }

        // Ensure snapshot directory exists
        if let Some(parent) = snapshot_path.parent() {
            fs::create_dir_all(parent).await?;
        }

        // Remove existing snapshot file if it exists
        let _ = fs::remove_file(&snapshot_path).await;

        // Create SQLite backup
        self.create_sqlite_backup(db_path, &snapshot_path).await?;

        // Register the snapshot (scope the lock)
        {
            let mut snapshots = self.inner.snapshots.lock();
            snapshots.insert(snapshot_path_str.clone(), SnapshotInfo::new());
            tracing::info!(
                "Registered snapshot: path={}, ref_count=1",
                snapshot_path_str
            );
        } // Lock is dropped here

        Ok((snapshot_path, cleanup_func))
    }

    async fn start_cleanup_loop(
        &self,
        ctx: CancellationToken,
        ttl: Duration,
        sqlite_base_dir: &Path,
    ) -> Result<()> {
        tracing::info!("Starting snapshot cleanup loop with TTL: {:?}", ttl);

        let snapshots_dir = sqlite_base_dir.join(SNAPSHOT_DIR_NAME);
        fs::create_dir_all(&snapshots_dir).await.map_err(|e| {
            tracing::warn!(
                "Failed to create snapshots directory: path={}, error={}",
                snapshots_dir.display(),
                e
            );
            e
        })?;

        let check_interval = ttl / 2;
        let mut interval_timer = interval(check_interval);

        loop {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Cleanup loop stopping due to context cancellation");
                    self.cleanup_stale_snapshots(ttl, &snapshots_dir).await;
                    return Ok(());
                }
                _ = interval_timer.tick() => {
                    self.cleanup_stale_snapshots(ttl, &snapshots_dir).await;
                }
            }
        }
    }
}

impl SnapshotManager {
    /// Create SQLite backup using rusqlite backup API
    async fn create_sqlite_backup(&self, source_path: &Path, dest_path: &Path) -> Result<()> {
        let source_path = source_path.to_path_buf();
        let dest_path = dest_path.to_path_buf();

        // Run backup in blocking task since rusqlite is synchronous
        tokio::task::spawn_blocking(move || {
            let backup_start = std::time::Instant::now();

            // Open source connection (read-only)
            let source_conn =
                Connection::open_with_flags(&source_path, OpenFlags::SQLITE_OPEN_READ_ONLY)?;

            // Open destination connection (create new)
            let mut dest_conn = Connection::open_with_flags(
                &dest_path,
                OpenFlags::SQLITE_OPEN_READ_WRITE | OpenFlags::SQLITE_OPEN_CREATE,
            )?;

            // Create backup
            let backup = Backup::new(&source_conn, &mut dest_conn)?;

            // Perform backup in steps (all pages at once with -1)
            backup.step(-1)?;

            // Finalize backup (backup is automatically finalized when dropped)

            tracing::info!(
                "SQLite backup created successfully: path={}, duration={:?}",
                dest_path.display(),
                backup_start.elapsed()
            );

            Ok::<(), crate::EmcacheError>(())
        })
        .await??;

        Ok(())
    }

    /// Clean up stale snapshots based on TTL and reference count
    async fn cleanup_stale_snapshots(&self, ttl: Duration, snapshots_dir: &Path) {
        tracing::debug!("Running periodic cleanup check");
        let now = SystemTime::now();
        let mut deleted_count = 0;

        let mut to_keep = std::collections::HashSet::new();

        // Determine which snapshots to keep (scope the lock)
        {
            let snapshots = self.inner.snapshots.lock();
            for (path, info) in snapshots.iter() {
                if info.ref_count > 0 {
                    if let Ok(elapsed) = now.duration_since(info.creation_time) {
                        if elapsed < ttl {
                            to_keep.insert(path.clone());
                        }
                    }
                }
            }
        } // Lock is dropped here

        // Read snapshot directory and identify files to delete
        let mut snapshots_to_delete = Vec::new();
        if let Ok(mut entries) = fs::read_dir(snapshots_dir).await {
            while let Ok(Some(entry)) = entries.next_entry().await {
                if let Ok(file_type) = entry.file_type().await {
                    if file_type.is_file() {
                        if let Some(file_name) = entry.file_name().to_str() {
                            if file_name.ends_with(".snapshot") {
                                let full_path = entry.path();
                                let full_path_str = full_path.to_string_lossy().to_string();
                                if !to_keep.contains(&full_path_str) {
                                    snapshots_to_delete.push(full_path);
                                }
                            }
                        }
                    }
                }
            }
        } else {
            tracing::error!(
                "Error reading snapshot directory: {}",
                snapshots_dir.display()
            );
        }

        // Delete stale snapshots
        for path in snapshots_to_delete {
            match fs::remove_file(&path).await {
                Ok(()) => {
                    tracing::info!("Deleted stale snapshot file: {}", path.display());
                    deleted_count += 1;
                }
                Err(e) => {
                    tracing::error!(
                        "Error deleting snapshot file: path={}, error={}",
                        path.display(),
                        e
                    );
                }
            }
        }

        if deleted_count > 0 {
            tracing::info!("Deleted stale snapshots: count={}", deleted_count);
        }
    }
}

/// Get the global snapshot manager instance
pub fn get_snapshot_manager() -> SnapshotManager {
    SnapshotManager::new()
}

/// Generate a snapshot filename for a collection
pub fn generate_snapshot_filename(collection_name: &str, version: i32) -> String {
    format!("{}_v{}.snapshot", collection_name, version)
}
