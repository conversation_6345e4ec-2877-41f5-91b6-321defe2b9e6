use dashmap::DashMap;
use std::{sync::Arc, time::Duration};
use tokio::{sync::broadcast, time::interval};

use crate::{
    config::CoordinatorConfig,
    db::{CollectionRepository, PostgresDatabase, ReplicatedCollection},
    Result,
};

/// Collection cache manager for storing and retrieving collection metadata
pub struct Manager {
    pg_db: Arc<PostgresDatabase>,
    refresh_interval: Duration,
    collections: Arc<DashMap<String, ReplicatedCollection>>,
    refresh_tx: broadcast::Sender<()>,
    _refresh_rx: broadcast::Receiver<()>, // Keep receiver to prevent channel from closing
}

impl Manager {
    pub fn new(pg_db: Arc<PostgresDatabase>, config: &CoordinatorConfig) -> Self {
        let refresh_interval = Duration::from_secs(config.collection_refresh_interval_secs);
        let (refresh_tx, refresh_rx) = broadcast::channel(1);

        Self {
            pg_db,
            refresh_interval,
            collections: Arc::new(DashMap::new()),
            refresh_tx,
            _refresh_rx: refresh_rx,
        }
    }

    /// Start the collection cache manager with background refresh loop
    pub async fn start(&self, ctx: tokio_util::sync::CancellationToken) -> Result<()> {
        tracing::info!("Collection cache starting");

        // Perform initial refresh
        if let Err(e) = self.refresh().await {
            tracing::error!("Initial fetch failed, cache might be empty: {}", e);
        } else {
            tracing::info!(
                "Initial fetch successful, collections_loaded={}",
                self.collections.len()
            );
        }

        // Start refresh loop
        let collections = self.collections.clone();
        let pg_db = self.pg_db.clone();
        let refresh_interval = self.refresh_interval;
        let refresh_tx = self.refresh_tx.clone();

        tokio::spawn(async move {
            tracing::info!("Refresh loop started, interval={:?}", refresh_interval);
            let mut interval_timer = interval(refresh_interval);

            loop {
                tokio::select! {
                    _ = ctx.cancelled() => {
                        tracing::info!("Context cancelled, refresh loop exiting");
                        return;
                    }
                    _ = interval_timer.tick() => {
                        match Self::refresh_collections(&pg_db, &collections).await {
                            Ok(count) => {
                                tracing::info!("Collections refreshed, count={}", count);
                                // Notify subscribers about refresh
                                let _ = refresh_tx.send(());
                            }
                            Err(e) => {
                                tracing::error!("Failed to refresh collections: {}", e);
                            }
                        }
                    }
                }
            }
        });

        Ok(())
    }

    /// Stop the collection cache manager
    pub async fn stop(&self) -> Result<()> {
        tracing::info!("Collection cache stopping");
        Ok(())
    }

    /// Get a collection by name
    pub async fn get_collection(&self, name: &str) -> Result<Option<ReplicatedCollection>> {
        if let Some(collection) = self.collections.get(name) {
            Ok(Some(collection.clone()))
        } else {
            Ok(None)
        }
    }

    /// Get all collections
    pub async fn get_all_collections(&self) -> Result<Vec<ReplicatedCollection>> {
        let collections: Vec<ReplicatedCollection> = self
            .collections
            .iter()
            .map(|entry| entry.value().clone())
            .collect();
        Ok(collections)
    }

    /// Get a collection with refresh if not found
    pub async fn get_collection_refresh(&self, name: &str) -> Result<Option<ReplicatedCollection>> {
        // First try to get from cache
        if let Some(collection) = self.collections.get(name) {
            return Ok(Some(collection.clone()));
        }

        // If not found, refresh cache and try again
        self.refresh().await?;

        if let Some(collection) = self.collections.get(name) {
            Ok(Some(collection.clone()))
        } else {
            Ok(None)
        }
    }

    /// Get a channel that receives notifications when cache is refreshed
    pub fn refresh_channel(&self) -> broadcast::Receiver<()> {
        self.refresh_tx.subscribe()
    }

    /// Refresh the cache from the database
    async fn refresh(&self) -> Result<usize> {
        Self::refresh_collections(&self.pg_db, &self.collections).await
    }

    /// Internal method to refresh collections from database
    async fn refresh_collections(
        pg_db: &PostgresDatabase,
        collections: &DashMap<String, ReplicatedCollection>,
    ) -> Result<usize> {
        let collections_list = pg_db.get_all_replicated_collections_with_shapes().await?;

        // Clear existing collections and insert new ones
        collections.clear();
        for collection in &collections_list {
            collections.insert(collection.collection_name.clone(), collection.clone());
        }

        Ok(collections_list.len())
    }
}
