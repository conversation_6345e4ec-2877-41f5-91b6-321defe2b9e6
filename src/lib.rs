pub mod config;
pub mod db;
pub mod leader;
pub mod follower;
pub mod coordinator;
pub mod collection_cache;
pub mod grpc_api;
pub mod snapshot;
pub mod migrations;
pub mod shape;
pub mod proto;
pub mod error;

pub use error::{EmcacheError, Result};

// Re-export commonly used types
pub use config::Config;
pub use db::{Database, PostgresDatabase};
pub use shape::{Shape, Column, Index, Filter, DataType};
