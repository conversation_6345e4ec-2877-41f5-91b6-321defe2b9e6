use async_trait::async_trait;
use sqlx::PgPool;
use std::time::Duration;

use crate::Result;

/// Interface for leader election functionality
#[async_trait]
pub trait LeaderElectorInterface {
    async fn try_acquire_lock(
        &self,
        collection_name: &str,
        lease_duration: Duration,
    ) -> Result<bool>;

    async fn release_lock(&self, collection_name: &str) -> Result<()>;

    async fn release_all(&self) -> Result<()>;

    async fn is_leader(&self, collection_name: &str, lease_duration: Duration) -> bool;
}

/// PostgreSQL-based leader election implementation
pub struct LeaderElector {
    pool: PgPool,
    instance_id: String,
}

impl LeaderElector {
    pub fn new(pool: PgPool, instance_id: String) -> Self {
        Self { pool, instance_id }
    }

    /// Generate a unique instance ID based on hostname and process ID
    pub fn generate_instance_id() -> String {
        let hostname = hostname::get()
            .map(|h| h.to_string_lossy().to_string())
            .unwrap_or_else(|_| "unknown-instance".to_string());

        let pid = std::process::id();
        format!("{}-{}", hostname, pid)
    }
}

#[async_trait]
impl LeaderElectorInterface for LeaderElector {
    async fn try_acquire_lock(
        &self,
        collection_name: &str,
        lease_duration: Duration,
    ) -> Result<bool> {
        let sql = r#"
            INSERT INTO leader_locks (collection_name, leader_id, lease_expires_at)
            VALUES ($1, $2, NOW() + $3 * interval '1 second')
            ON CONFLICT (collection_name) DO UPDATE SET
                leader_id = EXCLUDED.leader_id,
                lease_expires_at = EXCLUDED.lease_expires_at
            WHERE leader_locks.lease_expires_at < NOW() OR leader_locks.leader_id = $2
        "#;

        let lease_seconds = lease_duration.as_secs() as i32;

        // Use a timeout for the acquire operation
        let acquire_timeout = Duration::from_secs(5);
        let result = tokio::time::timeout(
            acquire_timeout,
            sqlx::query(sql)
                .bind(collection_name)
                .bind(&self.instance_id)
                .bind(lease_seconds)
                .execute(&self.pool),
        )
        .await;

        match result {
            Ok(Ok(query_result)) => {
                let acquired = query_result.rows_affected() > 0;
                if acquired {
                    tracing::info!(
                        "Leadership lease acquired/renewed: collection={}, duration={:?}",
                        collection_name,
                        lease_duration
                    );
                }
                Ok(acquired)
            }
            Ok(Err(e)) => {
                tracing::error!(
                    "Failed to acquire/renew leadership lock: collection={}, error={}",
                    collection_name,
                    e
                );
                Err(e.into())
            }
            Err(_) => {
                tracing::error!(
                    "Timeout acquiring leadership lock: collection={}",
                    collection_name
                );
                Err(crate::EmcacheError::Internal(
                    "Timeout acquiring leadership lock".to_string(),
                ))
            }
        }
    }

    async fn release_lock(&self, collection_name: &str) -> Result<()> {
        let sql = r#"
            DELETE FROM leader_locks
            WHERE collection_name = $1 AND leader_id = $2
        "#;

        let release_timeout = Duration::from_secs(1);
        let result = tokio::time::timeout(
            release_timeout,
            sqlx::query(sql)
                .bind(collection_name)
                .bind(&self.instance_id)
                .execute(&self.pool),
        )
        .await;

        match result {
            Ok(Ok(query_result)) => {
                if query_result.rows_affected() > 0 {
                    tracing::info!("Leadership lease released: collection={}", collection_name);
                } else {
                    tracing::info!(
                        "Leadership lease not found: collection={}, note=Not the leader or record did not exist",
                        collection_name
                    );
                }
                Ok(())
            }
            Ok(Err(e)) => {
                tracing::error!(
                    "Failed to delete leadership lease: collection={}, error={}",
                    collection_name,
                    e
                );
                Err(e.into())
            }
            Err(_) => {
                tracing::error!(
                    "Timeout releasing leadership lock: collection={}",
                    collection_name
                );
                Err(crate::EmcacheError::Internal(
                    "Timeout releasing leadership lock".to_string(),
                ))
            }
        }
    }

    async fn release_all(&self) -> Result<()> {
        tracing::info!(
            "Releasing all leadership leases: instance_id={}",
            self.instance_id
        );

        let sql = r#"
            DELETE FROM leader_locks
            WHERE leader_id = $1
        "#;

        let release_timeout = Duration::from_secs(1);
        let result = tokio::time::timeout(
            release_timeout,
            sqlx::query(sql).bind(&self.instance_id).execute(&self.pool),
        )
        .await;

        match result {
            Ok(Ok(query_result)) => {
                tracing::info!(
                    "Leadership leases released: instance_id={}, count={}",
                    self.instance_id,
                    query_result.rows_affected()
                );
                Ok(())
            }
            Ok(Err(e)) => {
                tracing::error!(
                    "Failed to release all leadership leases: instance_id={}, error={}",
                    self.instance_id,
                    e
                );
                Err(e.into())
            }
            Err(_) => {
                tracing::error!(
                    "Timeout releasing all leadership locks: instance_id={}",
                    self.instance_id
                );
                Err(crate::EmcacheError::Internal(
                    "Timeout releasing all leadership locks".to_string(),
                ))
            }
        }
    }

    async fn is_leader(&self, collection_name: &str, lease_duration: Duration) -> bool {
        let extend_sql = r#"
            UPDATE leader_locks SET lease_expires_at = NOW() + $3 * interval '1 second'
            WHERE collection_name = $1 AND leader_id = $2
        "#;

        let lease_seconds = lease_duration.as_secs() as i32;
        let extend_timeout = Duration::from_secs(1);

        let result = tokio::time::timeout(
            extend_timeout,
            sqlx::query(extend_sql)
                .bind(collection_name)
                .bind(&self.instance_id)
                .bind(lease_seconds)
                .execute(&self.pool),
        )
        .await;

        match result {
            Ok(Ok(query_result)) => {
                if query_result.rows_affected() == 0 {
                    tracing::warn!(
                        "Leadership lease extension failed: collection={}, reason=Lease might have expired",
                        collection_name
                    );
                    false
                } else {
                    true
                }
            }
            Ok(Err(e)) => {
                tracing::error!(
                    "Failed to extend leadership lease: collection={}, error={}",
                    collection_name,
                    e
                );
                false
            }
            Err(_) => {
                tracing::error!(
                    "Timeout extending leadership lease: collection={}",
                    collection_name
                );
                false
            }
        }
    }
}
