use async_trait::async_trait;
use dashmap::DashMap;
use std::sync::Arc;
use tokio_util::sync::CancellationToken;

use crate::{
    collection_cache::Manager as CollectionCacheManager,
    config::Config,
    db::{MongoClient, PostgresDatabase, ReplicatedCollection},
    leader::LeaderElector,
    Result,
};

pub mod manager;

use manager::CollectionManager;

/// Interface for coordinator functionality
#[async_trait]
pub trait CoordinatorInterface {
    async fn start(self: Arc<Self>, ctx: CancellationToken) -> Result<()>;
}

/// Options for creating a coordinator
pub struct CoordinatorOptions {
    pub pg_db: Arc<PostgresDatabase>,
    pub mongo_client: MongoClient,
    pub mongo_db_name: String,
    pub leader_elector: Arc<LeaderElector>,
    pub collection_cache: Arc<CollectionCacheManager>,
    pub config: Config,
}

/// Coordinator manages collection lifecycle and role transitions
pub struct Coordinator {
    pg_db: Arc<PostgresDatabase>,
    mongo_client: MongoClient,
    mongo_db_name: String,
    leader_elector: Arc<LeaderElector>,
    collection_cache: Arc<CollectionCacheManager>,
    config: Config,
    managed_collections: Arc<DashMap<String, CancellationToken>>,
}

impl Coordinator {
    pub fn new(opts: CoordinatorOptions) -> Self {
        Self {
            pg_db: opts.pg_db,
            mongo_client: opts.mongo_client,
            mongo_db_name: opts.mongo_db_name,
            leader_elector: opts.leader_elector,
            collection_cache: opts.collection_cache,
            config: opts.config,
            managed_collections: Arc::new(DashMap::new()),
        }
    }

    /// Synchronize collections based on cache state
    async fn sync_collections(&self, ctx: &CancellationToken) -> Result<()> {
        tracing::info!("Starting syncCollections");

        let cached_collections = self.collection_cache.get_all_collections().await?;
        let mut cached_collections_set = std::collections::HashMap::new();

        for coll in cached_collections {
            cached_collections_set.insert(coll.collection_name.clone(), coll);
        }

        // Get current managed collection names
        let current_managed_keys: Vec<String> = self
            .managed_collections
            .iter()
            .map(|entry| entry.key().clone())
            .collect();

        // Start managing new collections
        for (coll_name, replicated_coll) in &cached_collections_set {
            if !self.managed_collections.contains_key(coll_name) {
                tracing::info!(
                    "Detected new collection to manage from cache: {}",
                    coll_name
                );
                self.start_managing(ctx, replicated_coll.clone()).await;
            }
        }

        // Stop managing removed collections
        for managed_coll_key in current_managed_keys {
            if !cached_collections_set.contains_key(&managed_coll_key) {
                tracing::info!(
                    "Detected removed collection to stop managing: {}, reason=no longer in cache",
                    managed_coll_key
                );
                self.stop_managing(&managed_coll_key).await;
            }
        }

        Ok(())
    }

    /// Start managing a collection
    async fn start_managing(
        &self,
        parent_ctx: &CancellationToken,
        replicated_coll: ReplicatedCollection,
    ) {
        let collection_name = &replicated_coll.collection_name;

        if self.managed_collections.contains_key(collection_name) {
            tracing::info!("Collection is already being managed: {}", collection_name);
            return;
        }

        let coll_ctx = CancellationToken::new();
        let child_ctx = parent_ctx.child_token();

        // Store the cancellation token
        self.managed_collections
            .insert(collection_name.clone(), coll_ctx.clone());

        // Create collection manager
        let manager = CollectionManager::new(
            self.pg_db.clone(),
            self.mongo_client.clone(),
            self.mongo_db_name.clone(),
            self.leader_elector.clone(),
            self.config.clone(),
        );

        // Start managing the collection
        let collection_name_clone = collection_name.clone();
        let managed_collections = self.managed_collections.clone();

        tokio::spawn(async move {
            // Wait for either parent cancellation or collection-specific cancellation
            tokio::select! {
                _ = child_ctx.cancelled() => {
                    tracing::info!("Parent context cancelled for collection: {}", collection_name_clone);
                }
                _ = coll_ctx.cancelled() => {
                    tracing::info!("Collection context cancelled for: {}", collection_name_clone);
                }
                result = manager.manage_collection(replicated_coll, coll_ctx.clone()) => {
                    if let Err(e) = result {
                        tracing::error!("Collection management failed for {}: {}", collection_name_clone, e);
                    }
                }
            }

            // Clean up
            managed_collections.remove(&collection_name_clone);
            tracing::info!("Stopped managing collection: {}", collection_name_clone);
        });
    }

    /// Stop managing a collection
    async fn stop_managing(&self, collection_name: &str) {
        if let Some((_, cancel_token)) = self.managed_collections.remove(collection_name) {
            tracing::info!("Stopping management for collection: {}", collection_name);
            cancel_token.cancel();
        } else {
            tracing::info!("Collection is not currently managed: {}", collection_name);
        }
    }

    /// Stop managing all collections
    async fn stop_all_managing(&self) {
        tracing::info!(
            "Stopping management for collections, count={}",
            self.managed_collections.len()
        );

        let collection_names: Vec<String> = self
            .managed_collections
            .iter()
            .map(|entry| entry.key().clone())
            .collect();

        for name in collection_names {
            tracing::info!("Signaling stop during shutdown for collection: {}", name);
            if let Some((_, cancel_token)) = self.managed_collections.remove(&name) {
                cancel_token.cancel();
            }
        }
    }
}

#[async_trait]
impl CoordinatorInterface for Coordinator {
    async fn start(self: Arc<Self>, ctx: CancellationToken) -> Result<()> {
        tracing::info!("Starting Coordinator");

        // Get refresh channel from collection cache
        let mut refresh_rx = self.collection_cache.refresh_channel();

        loop {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Context cancelled. Stopping coordinator and managed collections");
                    self.stop_all_managing().await;
                    return Ok(());
                }
                _ = refresh_rx.recv() => {
                    tracing::info!("Received cache refresh signal. Running periodic collection sync");
                    if let Err(e) = self.sync_collections(&ctx).await {
                        tracing::error!("Error during periodic collection sync: {}", e);
                    } else {
                        tracing::info!("Periodic collection sync finished");
                    }
                }
            }
        }
    }
}
