use std::{sync::Arc, time::Duration};
use tokio::time::{interval, sleep};
use tokio_util::sync::CancellationToken;

use crate::{
    config::Config,
    db::{MongoClient, PostgresDatabase, ReplicatedCollection},
    leader::{LeaderE<PERSON><PERSON>, LeaderElectorInterface},
    Result,
};

/// Collection manager handles the lifecycle of a single collection
pub struct CollectionManager {
    pg_db: Arc<PostgresDatabase>,
    mongo_client: MongoClient,
    mongo_db_name: String,
    leader_elector: Arc<LeaderElector>,
    config: Config,
}

impl CollectionManager {
    pub fn new(
        pg_db: Arc<PostgresDatabase>,
        mongo_client: MongoClient,
        mongo_db_name: String,
        leader_elector: Arc<LeaderElector>,
        config: Config,
    ) -> Self {
        Self {
            pg_db,
            mongo_client,
            mongo_db_name,
            leader_elector,
            config,
        }
    }

    /// Manage a collection with leader/follower role transitions
    pub async fn manage_collection(
        &self,
        replicated_coll: ReplicatedCollection,
        ctx: CancellationToken,
    ) -> Result<()> {
        let collection_name = &replicated_coll.collection_name;
        let mut current_role = String::new();
        let mut role_ctx: Option<CancellationToken> = None;

        tracing::info!("Starting management routine for collection: {}", collection_name);

        let lease_duration = Duration::from_secs(self.config.leader.lease_duration_secs);
        let check_interval = lease_duration / 2;
        let mut interval_timer = interval(check_interval);

        loop {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Management context cancelled, stopping collection: {}", collection_name);
                    self.leader_elector.release_lock(collection_name).await?;
                    if let Some(role_cancel) = role_ctx {
                        role_cancel.cancel();
                    }
                    return Ok(());
                }
                _ = interval_timer.tick() => {
                    let mut is_leader_now = false;

                    // Check if we're already a leader
                    if self.leader_elector.is_leader(collection_name, lease_duration).await {
                        is_leader_now = true;
                    } else {
                        // Try to acquire leadership
                        match self.leader_elector.try_acquire_lock(collection_name, lease_duration).await {
                            Ok(acquired) => {
                                if acquired {
                                    is_leader_now = true;
                                }
                            }
                            Err(e) => {
                                tracing::error!(
                                    "Failed to acquire leadership for collection {}: {}",
                                    collection_name,
                                    e
                                );
                            }
                        }
                    }

                    let desired_role = if is_leader_now { "leader" } else { "follower" };

                    if desired_role != current_role {
                        tracing::info!(
                            "Transitioning role for collection {}: {} -> {}",
                            collection_name,
                            current_role,
                            desired_role
                        );

                        // Cancel previous role context
                        if let Some(role_cancel) = role_ctx.take() {
                            role_cancel.cancel();
                        }

                        current_role = desired_role.to_string();

                        if current_role == "leader" {
                            tracing::info!(
                                "Starting change stream listener for collection {}: Mongo->Postgres",
                                collection_name
                            );
                            let new_role_ctx = CancellationToken::new();
                            role_ctx = Some(new_role_ctx.clone());

                            // Start change stream listener
                            self.start_change_stream_listener(
                                replicated_coll.clone(),
                                new_role_ctx,
                            ).await?;
                        } else {
                            tracing::info!("Instance is now a follower for collection: {}", collection_name);
                            role_ctx = None;
                        }
                    }
                }
            }
        }
    }

    /// Start MongoDB change stream listener for leader role
    async fn start_change_stream_listener(
        &self,
        replicated_coll: ReplicatedCollection,
        ctx: CancellationToken,
    ) -> Result<()> {
        // TODO: Implement change stream listener
        // This would be similar to the Go implementation in leader.StartChangeStreamListener
        // For now, we'll just log that it would start
        tracing::info!(
            "Change stream listener would start for collection: {}",
            replicated_coll.collection_name
        );

        // Spawn a task that would handle the change stream
        let collection_name = replicated_coll.collection_name.clone();
        tokio::spawn(async move {
            tokio::select! {
                _ = ctx.cancelled() => {
                    tracing::info!("Change stream listener cancelled for collection: {}", collection_name);
                }
                _ = sleep(Duration::from_secs(1)) => {
                    // This would be the actual change stream processing loop
                    tracing::debug!("Change stream processing for collection: {}", collection_name);
                }
            }
        });

        Ok(())
    }
}
