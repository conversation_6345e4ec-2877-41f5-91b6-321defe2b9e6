use async_trait::async_trait;
use std::{path::PathBuf, sync::Arc};
use tokio::fs::File;
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Response, Status, Streaming};

use crate::{
    collection_cache::Manager as CollectionCacheManager,
    db::{CollectionRepository, OplogEntry, OplogRepository, PostgresDatabase},
    follower::{FollowerInterface, MainFollower},
    proto::{
        emcache_service_server::EmcacheService, AddCollectionRequest, AddCollectionResponse,
        Compression, DownloadDbRequest, DownloadDbResponse, GetCollectionsRequest,
        GetCollectionsResponse, GetOplogEntriesRequest, GetOplogEntriesResponse,
        RemoveCollectionRequest, RemoveCollectionResponse,
    },
};

pub mod conversions;
pub mod validation;

use conversions::*;
use validation::*;

/// gRPC server implementation for EmCache service
pub struct EmcacheServer {
    pg_db: Arc<PostgresDatabase>,
    sqlite_dir: PathBuf,
    collection_cache: Arc<CollectionCacheManager>,
    follower: Arc<MainFollower>,
}

impl EmcacheServer {
    pub fn new(
        pg_db: Arc<PostgresDatabase>,
        sqlite_dir: PathBuf,
        collection_cache: Arc<CollectionCacheManager>,
        follower: Arc<MainFollower>,
    ) -> Self {
        Self {
            pg_db,
            sqlite_dir,
            collection_cache,
            follower,
        }
    }
}

#[async_trait]
impl EmcacheService for EmcacheServer {
    type DownloadDbStream = ReceiverStream<std::result::Result<DownloadDbResponse, Status>>;

    async fn download_db(
        &self,
        request: Request<DownloadDbRequest>,
    ) -> std::result::Result<Response<Self::DownloadDbStream>, Status> {
        let req = request.into_inner();
        let collection_name = req.collection_name;

        if collection_name.is_empty() {
            return Err(Status::invalid_argument("Collection name cannot be empty"));
        }

        tracing::info!(
            "Download database request for collection: {}",
            collection_name
        );

        // Validate collection exists
        let collection = self
            .collection_cache
            .get_collection(&collection_name)
            .await
            .map_err(|e| Status::internal(format!("Failed to get collection: {}", e)))?;

        if collection.is_none() {
            return Err(Status::not_found(format!(
                "Collection '{}' not found",
                collection_name
            )));
        }

        let collection = collection.unwrap();

        // Ensure follower has connection for this collection
        self.follower
            .ensure_open_connection(&collection_name, collection.current_version)
            .await
            .map_err(|e| Status::internal(format!("Failed to ensure connection: {}", e)))?;

        // Get database file path
        let db_path = crate::follower::sqlite_helpers::get_collection_db_path(
            &collection_name,
            &self.sqlite_dir,
            collection.current_version,
        );

        if !db_path.exists() {
            return Err(Status::not_found(format!(
                "Database file not found for collection '{}'",
                collection_name
            )));
        }

        // Create stream for file chunks
        let (tx, rx) = tokio::sync::mpsc::channel(4);
        let db_path_clone = db_path.clone();
        let collection_name_clone = collection_name.clone();

        tokio::spawn(async move {
            if let Err(e) = stream_database_file(db_path_clone, tx).await {
                tracing::error!(
                    "Failed to stream database file for collection {}: {}",
                    collection_name_clone,
                    e
                );
            }
        });

        tracing::info!(
            "Starting to stream database for collection: {}",
            collection_name
        );
        Ok(Response::new(ReceiverStream::new(rx)))
    }

    async fn get_oplog_entries(
        &self,
        request: Request<GetOplogEntriesRequest>,
    ) -> std::result::Result<Response<GetOplogEntriesResponse>, Status> {
        let req = request.into_inner();
        let collection_names = req.collection_names;
        let after_index = req.after_index;
        let mut limit = req.limit;

        if collection_names.is_empty() {
            return Err(Status::invalid_argument(
                "At least one collection name must be provided",
            ));
        }

        if limit <= 0 {
            limit = 100;
            tracing::info!("Using default limit for oplog entries: {}", limit);
        } else if limit > 1000 {
            limit = 1000;
            tracing::info!("Capping requested limit: {} -> {}", req.limit, limit);
        }

        tracing::info!(
            "Fetching oplog entries: collections={:?}, after_index={}, limit={}",
            collection_names,
            after_index,
            limit
        );

        let entries = self
            .pg_db
            .get_oplog_entries_multiple_collections(&collection_names, after_index, limit as i32)
            .await
            .map_err(|e| Status::internal(format!("Failed to fetch oplog entries: {}", e)))?;

        let pb_entries = entries
            .into_iter()
            .map(convert_oplog_entry_to_proto)
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| Status::internal(format!("Failed to convert oplog entry: {}", e)))?;

        tracing::info!(
            "Returning oplog entries: count={}, collections={:?}",
            pb_entries.len(),
            collection_names
        );

        Ok(Response::new(GetOplogEntriesResponse {
            entries: pb_entries,
        }))
    }

    async fn get_collections(
        &self,
        _request: Request<GetCollectionsRequest>,
    ) -> std::result::Result<Response<GetCollectionsResponse>, Status> {
        tracing::info!("Fetching all collections");

        let collections = self
            .pg_db
            .get_all_replicated_collections_with_shapes()
            .await
            .map_err(|e| Status::internal(format!("Failed to fetch collections: {}", e)))?;

        let pb_collections = collections
            .into_iter()
            .map(convert_replicated_collection_to_proto)
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| Status::internal(format!("Failed to convert collection: {}", e)))?;

        Ok(Response::new(GetCollectionsResponse {
            collections: pb_collections,
        }))
    }

    async fn add_collection(
        &self,
        request: Request<AddCollectionRequest>,
    ) -> std::result::Result<Response<AddCollectionResponse>, Status> {
        let req = request.into_inner();
        let collection_name = req.collection_name;

        if collection_name.is_empty() {
            return Err(Status::invalid_argument("Collection name cannot be empty"));
        }

        let proto_shape = req
            .shape
            .ok_or_else(|| Status::invalid_argument("Shape definition cannot be empty"))?;

        tracing::info!("Adding collection: {}", collection_name);

        let shape = convert_proto_shape_to_internal(&proto_shape)
            .map_err(|e| Status::invalid_argument(format!("Invalid shape structure: {}", e)))?;

        // Validate shape
        validate_shape(&shape)
            .map_err(|e| Status::invalid_argument(format!("Shape validation failed: {}", e)))?;

        // Add collection to database
        self.pg_db
            .add_replicated_collection(&collection_name, &shape)
            .await
            .map_err(|e| {
                if e.to_string().contains("already exists") {
                    Status::already_exists(format!(
                        "Collection '{}' already exists",
                        collection_name
                    ))
                } else {
                    Status::internal(format!("Failed to add collection: {}", e))
                }
            })?;

        tracing::info!("Collection added successfully: {}", collection_name);

        Ok(Response::new(AddCollectionResponse {}))
    }

    async fn remove_collection(
        &self,
        request: Request<RemoveCollectionRequest>,
    ) -> std::result::Result<Response<RemoveCollectionResponse>, Status> {
        let req = request.into_inner();
        let collection_name = req.collection_name;

        if collection_name.is_empty() {
            return Err(Status::invalid_argument("Collection name cannot be empty"));
        }

        tracing::info!("Removing collection: {}", collection_name);

        self.pg_db
            .remove_replicated_collection(&collection_name)
            .await
            .map_err(|e| {
                if e.to_string().contains("not found") {
                    Status::not_found(format!("Collection '{}' not found", collection_name))
                } else {
                    Status::internal(format!("Failed to remove collection: {}", e))
                }
            })?;

        tracing::info!("Collection removed successfully: {}", collection_name);

        Ok(Response::new(RemoveCollectionResponse {}))
    }
}

/// Stream database file in chunks
async fn stream_database_file(
    db_path: PathBuf,
    tx: tokio::sync::mpsc::Sender<std::result::Result<DownloadDbResponse, Status>>,
) -> std::result::Result<(), Box<dyn std::error::Error + Send + Sync>> {
    use tokio::io::AsyncReadExt;

    let mut file = File::open(&db_path).await?;
    let chunk_size = 1024 * 1024 * 5; // 5MB chunks
    let mut buffer = vec![0; chunk_size];

    loop {
        let bytes_read = file.read(&mut buffer).await?;
        if bytes_read == 0 {
            break; // EOF
        }

        let chunk = buffer[..bytes_read].to_vec();
        let response = DownloadDbResponse {
            version: 1, // TODO: Use actual collection version
            chunk,
            compression: Compression::None as i32,
        };

        if tx.send(Ok(response)).await.is_err() {
            break; // Receiver dropped
        }
    }

    Ok(())
}
