use prost_types::Timestamp;

use crate::{
    db::{OplogEntry, ReplicatedCollection},
    proto::{
        Collection as ProtoCollection, Column as ProtoColumn, DataType as ProtoDataType,
        Index as ProtoIndex, OplogEntry as ProtoOplogEntry, <PERSON><PERSON><PERSON> as ProtoSha<PERSON>,
    },
    shape::{Column, DataType, Index, Shape},
    Result,
};

/// Convert internal OplogEntry to protobuf OplogEntry
pub fn convert_oplog_entry_to_proto(entry: OplogEntry) -> Result<ProtoOplogEntry> {
    let timestamp = Timestamp {
        seconds: entry.created_at.timestamp(),
        nanos: entry.created_at.timestamp_subsec_nanos() as i32,
    };

    // Convert operation string to enum
    let operation = match entry.operation.as_str() {
        "UPSERT" => 0, // OperationType::Upsert
        "DELETE" => 1, // OperationType::Delete
        _ => {
            return Err(crate::EmcacheError::Internal(format!(
                "Unknown operation: {}",
                entry.operation
            )))
        }
    };

    // Convert document to protobuf Struct if present
    let data = if let Some(doc_bytes) = entry.doc {
        // For now, we'll skip the complex JSON to Struct conversion
        // This would need proper implementation based on the document structure
        None
    } else {
        None
    };

    Ok(ProtoOplogEntry {
        index: entry.id,
        operation,
        id: entry.doc_id,
        timestamp: Some(timestamp),
        collection: entry.collection,
        data,
        version: entry.version,
    })
}

/// Convert internal ReplicatedCollection to protobuf Collection
pub fn convert_replicated_collection_to_proto(
    collection: ReplicatedCollection,
) -> Result<ProtoCollection> {
    let proto_shape = convert_shape_to_proto(&collection.shape)?;

    Ok(ProtoCollection {
        name: collection.collection_name,
        version: collection.current_version,
        shape: Some(proto_shape),
    })
}

/// Convert internal Shape to protobuf Shape
pub fn convert_shape_to_proto(shape: &Shape) -> Result<ProtoShape> {
    let columns = shape
        .columns
        .iter()
        .map(convert_column_to_proto)
        .collect::<Result<Vec<_>>>()?;

    let indexes = shape
        .indexes
        .iter()
        .map(convert_index_to_proto)
        .collect::<Result<Vec<_>>>()?;

    Ok(ProtoShape {
        columns,
        indexes,
        filters: vec![], // Empty filters for now
    })
}

/// Convert internal Column to protobuf Column
pub fn convert_column_to_proto(column: &Column) -> Result<ProtoColumn> {
    let data_type = convert_data_type_to_proto(&column.data_type);

    Ok(ProtoColumn {
        name: column.name.clone(),
        r#type: data_type as i32,
        path: column.path.clone(),
    })
}

/// Convert internal Index to protobuf Index
pub fn convert_index_to_proto(index: &Index) -> Result<ProtoIndex> {
    Ok(ProtoIndex {
        columns: index.columns.clone(),
    })
}

/// Convert internal DataType to protobuf DataType
pub fn convert_data_type_to_proto(data_type: &DataType) -> ProtoDataType {
    match data_type {
        DataType::Any => ProtoDataType::Any,
        DataType::Bool => ProtoDataType::Bool,
        DataType::Number => ProtoDataType::Number,
        DataType::Integer => ProtoDataType::Integer,
        DataType::Text => ProtoDataType::Text,
        DataType::Jsonb => ProtoDataType::Jsonb,
    }
}

/// Convert protobuf Shape to internal Shape
pub fn convert_proto_shape_to_internal(proto_shape: &ProtoShape) -> Result<Shape> {
    let columns = proto_shape
        .columns
        .iter()
        .map(convert_proto_column_to_internal)
        .collect::<Result<Vec<_>>>()?;

    let indexes = proto_shape
        .indexes
        .iter()
        .map(convert_proto_index_to_internal)
        .collect::<Result<Vec<_>>>()?;

    Ok(Shape {
        columns,
        indexes,
        filters: vec![], // Empty filters for now
    })
}

/// Convert protobuf Column to internal Column
pub fn convert_proto_column_to_internal(proto_column: &ProtoColumn) -> Result<Column> {
    let data_type = convert_proto_data_type_to_internal(proto_column.r#type)?;

    Ok(Column {
        name: proto_column.name.clone(),
        data_type,
        path: proto_column.path.clone(),
    })
}

/// Convert protobuf Index to internal Index
pub fn convert_proto_index_to_internal(proto_index: &ProtoIndex) -> Result<Index> {
    Ok(Index {
        columns: proto_index.columns.clone(),
    })
}

/// Convert protobuf DataType to internal DataType
pub fn convert_proto_data_type_to_internal(proto_data_type: i32) -> Result<DataType> {
    match ProtoDataType::try_from(proto_data_type) {
        Ok(ProtoDataType::Any) => Ok(DataType::Any),
        Ok(ProtoDataType::Bool) => Ok(DataType::Bool),
        Ok(ProtoDataType::Number) => Ok(DataType::Number),
        Ok(ProtoDataType::Integer) => Ok(DataType::Integer),
        Ok(ProtoDataType::Text) => Ok(DataType::Text),
        Ok(ProtoDataType::Jsonb) => Ok(DataType::Jsonb),
        Err(_) => Err(crate::EmcacheError::Internal(format!(
            "Invalid data type: {}",
            proto_data_type
        ))),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_convert_data_type_roundtrip() {
        let data_types = vec![
            DataType::Any,
            DataType::Bool,
            DataType::Number,
            DataType::Integer,
            DataType::Text,
            DataType::Jsonb,
        ];

        for dt in data_types {
            let proto_dt = convert_data_type_to_proto(&dt);
            let converted_back = convert_proto_data_type_to_internal(proto_dt as i32).unwrap();
            assert_eq!(dt, converted_back);
        }
    }

    #[test]
    fn test_convert_column_roundtrip() {
        let column = Column {
            name: "test_column".to_string(),
            data_type: DataType::Text,
            path: "test.path".to_string(),
        };

        let proto_column = convert_column_to_proto(&column).unwrap();
        let converted_back = convert_proto_column_to_internal(&proto_column).unwrap();

        assert_eq!(column.name, converted_back.name);
        assert_eq!(column.data_type, converted_back.data_type);
        assert_eq!(column.path, converted_back.path);
    }

    #[test]
    fn test_convert_shape_roundtrip() {
        let shape = Shape {
            columns: vec![
                Column {
                    name: "id".to_string(),
                    data_type: DataType::Text,
                    path: "_id".to_string(),
                },
                Column {
                    name: "name".to_string(),
                    data_type: DataType::Text,
                    path: "name".to_string(),
                },
            ],
            indexes: vec![Index {
                columns: vec!["id".to_string()],
            }],
        };

        let proto_shape = convert_shape_to_proto(&shape).unwrap();
        let converted_back = convert_proto_shape_to_internal(&proto_shape).unwrap();

        assert_eq!(shape.columns.len(), converted_back.columns.len());
        assert_eq!(shape.indexes.len(), converted_back.indexes.len());
    }
}
