use std::collections::HashSet;

use crate::{shape::<PERSON><PERSON><PERSON>, Result};

/// Validate a shape definition
pub fn validate_shape(shape: &Shape) -> Result<()> {
    if shape.columns.is_empty() {
        return Err(crate::EmcacheError::Validation(
            "<PERSON><PERSON><PERSON> must have at least one column".to_string(),
        ));
    }

    // Check for duplicate column names
    let mut column_names = HashSet::new();
    for column in &shape.columns {
        if column.name.is_empty() {
            return Err(crate::EmcacheError::Validation(
                "Column name cannot be empty".to_string(),
            ));
        }

        if column.path.is_empty() {
            return Err(crate::EmcacheError::Validation(
                "Column path cannot be empty".to_string(),
            ));
        }

        if !column_names.insert(&column.name) {
            return Err(crate::EmcacheError::Validation(format!(
                "Duplicate column name: {}",
                column.name
            )));
        }

        // Validate column name format (alphanumeric and underscore only)
        if !column.name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(crate::EmcacheError::Validation(format!(
                "Invalid column name '{}': only alphanumeric characters and underscores are allowed",
                column.name
            )));
        }

        // Column name cannot start with a number
        if column.name.chars().next().unwrap_or('_').is_ascii_digit() {
            return Err(crate::EmcacheError::Validation(format!(
                "Invalid column name '{}': cannot start with a number",
                column.name
            )));
        }
    }

    // Validate indexes
    for (idx, index) in shape.indexes.iter().enumerate() {
        if index.columns.is_empty() {
            return Err(crate::EmcacheError::Validation(format!(
                "Index {} must have at least one column",
                idx
            )));
        }

        // Check that all index columns exist in the shape
        for index_column in &index.columns {
            if !column_names.contains(index_column) {
                return Err(crate::EmcacheError::Validation(format!(
                    "Index {} references non-existent column: {}",
                    idx, index_column
                )));
            }
        }
    }

    Ok(())
}

/// Validate collection name
pub fn validate_collection_name(name: &str) -> Result<()> {
    if name.is_empty() {
        return Err(crate::EmcacheError::Validation(
            "Collection name cannot be empty".to_string(),
        ));
    }

    if name.len() > 64 {
        return Err(crate::EmcacheError::Validation(
            "Collection name cannot be longer than 64 characters".to_string(),
        ));
    }

    // Collection name should only contain alphanumeric characters, underscores, and hyphens
    if !name.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '-') {
        return Err(crate::EmcacheError::Validation(
            "Collection name can only contain alphanumeric characters, underscores, and hyphens".to_string(),
        ));
    }

    // Cannot start with a number or hyphen
    let first_char = name.chars().next().unwrap();
    if first_char.is_ascii_digit() || first_char == '-' {
        return Err(crate::EmcacheError::Validation(
            "Collection name cannot start with a number or hyphen".to_string(),
        ));
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::shape::{Column, DataType, Index};

    #[test]
    fn test_validate_shape_valid() {
        let shape = Shape {
            columns: vec![
                Column {
                    name: "id".to_string(),
                    data_type: DataType::Text,
                    path: "_id".to_string(),
                },
                Column {
                    name: "name".to_string(),
                    data_type: DataType::Text,
                    path: "name".to_string(),
                },
            ],
            indexes: vec![Index {
                columns: vec!["id".to_string()],
            }],
        };

        assert!(validate_shape(&shape).is_ok());
    }

    #[test]
    fn test_validate_shape_empty_columns() {
        let shape = Shape {
            columns: vec![],
            indexes: vec![],
        };

        assert!(validate_shape(&shape).is_err());
    }

    #[test]
    fn test_validate_shape_duplicate_columns() {
        let shape = Shape {
            columns: vec![
                Column {
                    name: "id".to_string(),
                    data_type: DataType::Text,
                    path: "_id".to_string(),
                },
                Column {
                    name: "id".to_string(), // Duplicate
                    data_type: DataType::Integer,
                    path: "id".to_string(),
                },
            ],
            indexes: vec![],
        };

        assert!(validate_shape(&shape).is_err());
    }

    #[test]
    fn test_validate_shape_invalid_column_name() {
        let shape = Shape {
            columns: vec![Column {
                name: "123invalid".to_string(), // Starts with number
                data_type: DataType::Text,
                path: "field".to_string(),
            }],
            indexes: vec![],
        };

        assert!(validate_shape(&shape).is_err());
    }

    #[test]
    fn test_validate_shape_invalid_index_column() {
        let shape = Shape {
            columns: vec![Column {
                name: "id".to_string(),
                data_type: DataType::Text,
                path: "_id".to_string(),
            }],
            indexes: vec![Index {
                columns: vec!["nonexistent".to_string()], // References non-existent column
            }],
        };

        assert!(validate_shape(&shape).is_err());
    }

    #[test]
    fn test_validate_collection_name_valid() {
        assert!(validate_collection_name("users").is_ok());
        assert!(validate_collection_name("user_orders").is_ok());
        assert!(validate_collection_name("user-orders").is_ok());
        assert!(validate_collection_name("collection123").is_ok());
    }

    #[test]
    fn test_validate_collection_name_invalid() {
        assert!(validate_collection_name("").is_err()); // Empty
        assert!(validate_collection_name("123users").is_err()); // Starts with number
        assert!(validate_collection_name("-users").is_err()); // Starts with hyphen
        assert!(validate_collection_name("user@orders").is_err()); // Invalid character
        assert!(validate_collection_name(&"a".repeat(65)).is_err()); // Too long
    }
}
