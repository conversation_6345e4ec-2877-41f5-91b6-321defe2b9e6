use serde::{Deserialize, Serialize};
use std::env;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Ser<PERSON>ize, Deserialize, Validate)]
pub struct Config {
    #[validate(length(min = 1))]
    pub postgres_url: String,

    #[validate(length(min = 1))]
    pub mongo_url: String,

    #[validate(length(min = 1))]
    pub grpc_port: String,

    #[validate(length(min = 1))]
    pub sqlite_dir: String,

    #[validate(custom(function = "validate_log_level"))]
    pub log_level: String,

    #[validate(nested)]
    pub coordinator: CoordinatorConfig,

    #[validate(nested)]
    pub leader: LeaderConfig,

    #[validate(nested)]
    pub follower: FollowerConfig,

    #[validate(nested)]
    pub snapshot: SnapshotConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct CoordinatorConfig {
    #[validate(range(min = 1))]
    pub collection_refresh_interval_secs: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Validate)]
pub struct LeaderConfig {
    #[validate(range(min = 1))]
    pub resume_token_update_interval_secs: u64,

    #[validate(range(min = 1))]
    pub initial_scan_batch_size: u32,

    #[validate(range(min = 1))]
    pub lease_duration_secs: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct FollowerConfig {
    #[validate(range(min = 1))]
    pub poll_interval_secs: u64,

    #[validate(range(min = 1))]
    pub batch_size: u32,

    #[validate(range(min = 1))]
    pub cleanup_interval_secs: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct SnapshotConfig {
    #[validate(range(min = 1))]
    pub ttl_secs: u64,
}

fn validate_log_level(level: &str) -> Result<(), validator::ValidationError> {
    match level.to_uppercase().as_str() {
        "DEBUG" | "INFO" | "WARN" | "ERROR" => Ok(()),
        _ => Err(validator::ValidationError::new("invalid_log_level")),
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            postgres_url: String::new(),
            mongo_url: String::new(),
            grpc_port: ":50051".to_string(),
            sqlite_dir: "./emcache_dbs".to_string(),
            log_level: "INFO".to_string(),
            coordinator: CoordinatorConfig::default(),
            leader: LeaderConfig::default(),
            follower: FollowerConfig::default(),
            snapshot: SnapshotConfig::default(),
        }
    }
}

impl Default for CoordinatorConfig {
    fn default() -> Self {
        Self {
            collection_refresh_interval_secs: 60,
        }
    }
}

impl Default for LeaderConfig {
    fn default() -> Self {
        Self {
            resume_token_update_interval_secs: 10,
            initial_scan_batch_size: 1000,
            lease_duration_secs: 30,
        }
    }
}

impl Default for FollowerConfig {
    fn default() -> Self {
        Self {
            poll_interval_secs: 2,
            batch_size: 100,
            cleanup_interval_secs: 300,
        }
    }
}

impl Default for SnapshotConfig {
    fn default() -> Self {
        Self { ttl_secs: 3600 }
    }
}

impl Config {
    pub fn load() -> crate::Result<Self> {
        let mut settings = config::Config::builder()
            .add_source(config::Config::try_from(&Config::default())?)
            .add_source(config::Environment::with_prefix("EMCACHE").separator("_"));

        // Try to load from config file if specified
        if let Ok(config_path) = env::var("EMCACHE_CONFIG_PATH") {
            tracing::info!("Loading configuration from specified file: {}", config_path);
            settings = settings.add_source(config::File::with_name(&config_path).required(false));
        } else {
            // Try default config file locations
            settings = settings
                .add_source(config::File::with_name("config").required(false))
                .add_source(config::File::with_name("./config/config").required(false))
                .add_source(config::File::with_name("/etc/emcache/config").required(false));
        }

        let config: Config = settings.build()?.try_deserialize()?;

        // Validate the configuration
        config
            .validate()
            .map_err(|e| crate::EmcacheError::Validation(format!("{}", e)))?;

        tracing::info!("Configuration loaded and validated successfully");
        tracing::debug!("Final configuration: {:?}", config);

        Ok(config)
    }
}
