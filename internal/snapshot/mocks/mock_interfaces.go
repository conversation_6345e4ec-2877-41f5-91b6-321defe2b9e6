// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	sync "sync"

	gomock "go.uber.org/mock/gomock"
)

// MockSnapshotManager is a mock of SnapshotManager interface.
type MockSnapshotManager struct {
	ctrl     *gomock.Controller
	recorder *MockSnapshotManagerMockRecorder
	isgomock struct{}
}

// MockSnapshotManagerMockRecorder is the mock recorder for MockSnapshotManager.
type MockSnapshotManagerMockRecorder struct {
	mock *MockSnapshotManager
}

// NewMockSnapshotManager creates a new mock instance.
func NewMockSnapshotManager(ctrl *gomock.Controller) *MockSnapshotManager {
	mock := &MockSnapshotManager{ctrl: ctrl}
	mock.recorder = &MockSnapshotManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSnapshotManager) EXPECT() *MockSnapshotManagerMockRecorder {
	return m.recorder
}

// GetOrGenerateSnapshot mocks base method.
func (m *MockSnapshotManager) GetOrGenerateSnapshot(ctx context.Context, dbPath, snapshotDir, snapshotFileName string) (string, func(), error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrGenerateSnapshot", ctx, dbPath, snapshotDir, snapshotFileName)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(func())
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetOrGenerateSnapshot indicates an expected call of GetOrGenerateSnapshot.
func (mr *MockSnapshotManagerMockRecorder) GetOrGenerateSnapshot(ctx, dbPath, snapshotDir, snapshotFileName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrGenerateSnapshot", reflect.TypeOf((*MockSnapshotManager)(nil).GetOrGenerateSnapshot), ctx, dbPath, snapshotDir, snapshotFileName)
}

// StartCleanupLoop mocks base method.
func (m *MockSnapshotManager) StartCleanupLoop(ctx context.Context, wg *sync.WaitGroup, ttl int64, snapshotDir string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartCleanupLoop", ctx, wg, ttl, snapshotDir)
}

// StartCleanupLoop indicates an expected call of StartCleanupLoop.
func (mr *MockSnapshotManagerMockRecorder) StartCleanupLoop(ctx, wg, ttl, snapshotDir any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartCleanupLoop", reflect.TypeOf((*MockSnapshotManager)(nil).StartCleanupLoop), ctx, wg, ttl, snapshotDir)
}
