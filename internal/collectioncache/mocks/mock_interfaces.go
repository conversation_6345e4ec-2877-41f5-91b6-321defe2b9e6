// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db "github.com/nrjais/emcache/internal/db"
	gomock "go.uber.org/mock/gomock"
)

// MockCollectionCacheManager is a mock of CollectionCacheManager interface.
type MockCollectionCacheManager struct {
	ctrl     *gomock.Controller
	recorder *MockCollectionCacheManagerMockRecorder
	isgomock struct{}
}

// MockCollectionCacheManagerMockRecorder is the mock recorder for MockCollectionCacheManager.
type MockCollectionCacheManagerMockRecorder struct {
	mock *MockCollectionCacheManager
}

// NewMockCollectionCacheManager creates a new mock instance.
func NewMockCollectionCacheManager(ctrl *gomock.Controller) *MockCollectionCacheManager {
	mock := &MockCollectionCacheManager{ctrl: ctrl}
	mock.recorder = &MockCollectionCacheManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectionCacheManager) EXPECT() *MockCollectionCacheManagerMockRecorder {
	return m.recorder
}

// GetAllCollections mocks base method.
func (m *MockCollectionCacheManager) GetAllCollections() []db.ReplicatedCollection {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCollections")
	ret0, _ := ret[0].([]db.ReplicatedCollection)
	return ret0
}

// GetAllCollections indicates an expected call of GetAllCollections.
func (mr *MockCollectionCacheManagerMockRecorder) GetAllCollections() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCollections", reflect.TypeOf((*MockCollectionCacheManager)(nil).GetAllCollections))
}

// GetCollection mocks base method.
func (m *MockCollectionCacheManager) GetCollection(name string) (db.ReplicatedCollection, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollection", name)
	ret0, _ := ret[0].(db.ReplicatedCollection)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetCollection indicates an expected call of GetCollection.
func (mr *MockCollectionCacheManagerMockRecorder) GetCollection(name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollection", reflect.TypeOf((*MockCollectionCacheManager)(nil).GetCollection), name)
}

// GetCollectionRefresh mocks base method.
func (m *MockCollectionCacheManager) GetCollectionRefresh(ctx context.Context, name string) (db.ReplicatedCollection, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollectionRefresh", ctx, name)
	ret0, _ := ret[0].(db.ReplicatedCollection)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCollectionRefresh indicates an expected call of GetCollectionRefresh.
func (mr *MockCollectionCacheManagerMockRecorder) GetCollectionRefresh(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollectionRefresh", reflect.TypeOf((*MockCollectionCacheManager)(nil).GetCollectionRefresh), ctx, name)
}

// RefreshChannel mocks base method.
func (m *MockCollectionCacheManager) RefreshChannel() <-chan struct{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshChannel")
	ret0, _ := ret[0].(<-chan struct{})
	return ret0
}

// RefreshChannel indicates an expected call of RefreshChannel.
func (mr *MockCollectionCacheManagerMockRecorder) RefreshChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshChannel", reflect.TypeOf((*MockCollectionCacheManager)(nil).RefreshChannel))
}

// Start mocks base method.
func (m *MockCollectionCacheManager) Start(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Start", ctx)
}

// Start indicates an expected call of Start.
func (mr *MockCollectionCacheManagerMockRecorder) Start(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockCollectionCacheManager)(nil).Start), ctx)
}

// Stop mocks base method.
func (m *MockCollectionCacheManager) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockCollectionCacheManagerMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockCollectionCacheManager)(nil).Stop))
}
