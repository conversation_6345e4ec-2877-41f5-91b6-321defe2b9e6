// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	sync "sync"

	pgxpool "github.com/jackc/pgx/v5/pgxpool"
	config "github.com/nrjais/emcache/internal/config"
	db "github.com/nrjais/emcache/internal/db"
	leader "github.com/nrjais/emcache/internal/leader"
	mongo "go.mongodb.org/mongo-driver/mongo"
	gomock "go.uber.org/mock/gomock"
)

// MockCollectionManager is a mock of CollectionManager interface.
type MockCollectionManager struct {
	ctrl     *gomock.Controller
	recorder *MockCollectionManagerMockRecorder
	isgomock struct{}
}

// MockCollectionManagerMockRecorder is the mock recorder for MockCollectionManager.
type MockCollectionManagerMockRecorder struct {
	mock *MockCollectionManager
}

// NewMockCollectionManager creates a new mock instance.
func NewMockCollectionManager(ctrl *gomock.Controller) *MockCollectionManager {
	mock := &MockCollectionManager{ctrl: ctrl}
	mock.recorder = &MockCollectionManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectionManager) EXPECT() *MockCollectionManagerMockRecorder {
	return m.recorder
}

// ManageCollection mocks base method.
func (m *MockCollectionManager) ManageCollection(ctx context.Context, wg *sync.WaitGroup, replicatedColl db.ReplicatedCollection, pgPool *pgxpool.Pool, mongoClient *mongo.Client, mongoDBName string, leaderElector *leader.LeaderElector, cfg *config.Config) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ManageCollection", ctx, wg, replicatedColl, pgPool, mongoClient, mongoDBName, leaderElector, cfg)
}

// ManageCollection indicates an expected call of ManageCollection.
func (mr *MockCollectionManagerMockRecorder) ManageCollection(ctx, wg, replicatedColl, pgPool, mongoClient, mongoDBName, leaderElector, cfg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ManageCollection", reflect.TypeOf((*MockCollectionManager)(nil).ManageCollection), ctx, wg, replicatedColl, pgPool, mongoClient, mongoDBName, leaderElector, cfg)
}
