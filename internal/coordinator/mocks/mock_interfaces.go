// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockCoordinatorInterface is a mock of CoordinatorInterface interface.
type MockCoordinatorInterface struct {
	ctrl     *gomock.Controller
	recorder *MockCoordinatorInterfaceMockRecorder
	isgomock struct{}
}

// MockCoordinatorInterfaceMockRecorder is the mock recorder for MockCoordinatorInterface.
type MockCoordinatorInterfaceMockRecorder struct {
	mock *MockCoordinatorInterface
}

// NewMockCoordinatorInterface creates a new mock instance.
func NewMockCoordinatorInterface(ctrl *gomock.Controller) *MockCoordinatorInterface {
	mock := &MockCoordinatorInterface{ctrl: ctrl}
	mock.recorder = &MockCoordinatorInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCoordinatorInterface) EXPECT() *MockCoordinatorInterfaceMockRecorder {
	return m.recorder
}

// Start mocks base method.
func (m *MockCoordinatorInterface) Start(ctx context.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Start", ctx)
}

// Start indicates an expected call of Start.
func (mr *MockCoordinatorInterfaceMockRecorder) Start(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockCoordinatorInterface)(nil).Start), ctx)
}
