// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	pgxpool "github.com/jackc/pgx/v5/pgxpool"
	collectioncache "github.com/nrjais/emcache/internal/collectioncache"
	protos "github.com/nrjais/emcache/pkg/protos"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEmcacheServerInterface is a mock of EmcacheServerInterface interface.
type MockEmcacheServerInterface struct {
	ctrl     *gomock.Controller
	recorder *MockEmcacheServerInterfaceMockRecorder
	isgomock struct{}
}

// MockEmcacheServerInterfaceMockRecorder is the mock recorder for MockEmcacheServerInterface.
type MockEmcacheServerInterfaceMockRecorder struct {
	mock *MockEmcacheServerInterface
}

// NewMockEmcacheServerInterface creates a new mock instance.
func NewMockEmcacheServerInterface(ctrl *gomock.Controller) *MockEmcacheServerInterface {
	mock := &MockEmcacheServerInterface{ctrl: ctrl}
	mock.recorder = &MockEmcacheServerInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmcacheServerInterface) EXPECT() *MockEmcacheServerInterfaceMockRecorder {
	return m.recorder
}

// AddCollection mocks base method.
func (m *MockEmcacheServerInterface) AddCollection(arg0 context.Context, arg1 *protos.AddCollectionRequest) (*protos.AddCollectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCollection", arg0, arg1)
	ret0, _ := ret[0].(*protos.AddCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCollection indicates an expected call of AddCollection.
func (mr *MockEmcacheServerInterfaceMockRecorder) AddCollection(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCollection", reflect.TypeOf((*MockEmcacheServerInterface)(nil).AddCollection), arg0, arg1)
}

// DownloadDb mocks base method.
func (m *MockEmcacheServerInterface) DownloadDb(arg0 *protos.DownloadDbRequest, arg1 grpc.ServerStreamingServer[protos.DownloadDbResponse]) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadDb", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DownloadDb indicates an expected call of DownloadDb.
func (mr *MockEmcacheServerInterfaceMockRecorder) DownloadDb(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDb", reflect.TypeOf((*MockEmcacheServerInterface)(nil).DownloadDb), arg0, arg1)
}

// GetCollections mocks base method.
func (m *MockEmcacheServerInterface) GetCollections(arg0 context.Context, arg1 *protos.GetCollectionsRequest) (*protos.GetCollectionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollections", arg0, arg1)
	ret0, _ := ret[0].(*protos.GetCollectionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCollections indicates an expected call of GetCollections.
func (mr *MockEmcacheServerInterfaceMockRecorder) GetCollections(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollections", reflect.TypeOf((*MockEmcacheServerInterface)(nil).GetCollections), arg0, arg1)
}

// GetOplogEntries mocks base method.
func (m *MockEmcacheServerInterface) GetOplogEntries(arg0 context.Context, arg1 *protos.GetOplogEntriesRequest) (*protos.GetOplogEntriesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntries", arg0, arg1)
	ret0, _ := ret[0].(*protos.GetOplogEntriesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntries indicates an expected call of GetOplogEntries.
func (mr *MockEmcacheServerInterfaceMockRecorder) GetOplogEntries(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntries", reflect.TypeOf((*MockEmcacheServerInterface)(nil).GetOplogEntries), arg0, arg1)
}

// RemoveCollection mocks base method.
func (m *MockEmcacheServerInterface) RemoveCollection(arg0 context.Context, arg1 *protos.RemoveCollectionRequest) (*protos.RemoveCollectionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveCollection", arg0, arg1)
	ret0, _ := ret[0].(*protos.RemoveCollectionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveCollection indicates an expected call of RemoveCollection.
func (mr *MockEmcacheServerInterfaceMockRecorder) RemoveCollection(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveCollection", reflect.TypeOf((*MockEmcacheServerInterface)(nil).RemoveCollection), arg0, arg1)
}

// mustEmbedUnimplementedEmcacheServiceServer mocks base method.
func (m *MockEmcacheServerInterface) mustEmbedUnimplementedEmcacheServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEmcacheServiceServer")
}

// mustEmbedUnimplementedEmcacheServiceServer indicates an expected call of mustEmbedUnimplementedEmcacheServiceServer.
func (mr *MockEmcacheServerInterfaceMockRecorder) mustEmbedUnimplementedEmcacheServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEmcacheServiceServer", reflect.TypeOf((*MockEmcacheServerInterface)(nil).mustEmbedUnimplementedEmcacheServiceServer))
}

// MockServerFactory is a mock of ServerFactory interface.
type MockServerFactory struct {
	ctrl     *gomock.Controller
	recorder *MockServerFactoryMockRecorder
	isgomock struct{}
}

// MockServerFactoryMockRecorder is the mock recorder for MockServerFactory.
type MockServerFactoryMockRecorder struct {
	mock *MockServerFactory
}

// NewMockServerFactory creates a new mock instance.
func NewMockServerFactory(ctrl *gomock.Controller) *MockServerFactory {
	mock := &MockServerFactory{ctrl: ctrl}
	mock.recorder = &MockServerFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServerFactory) EXPECT() *MockServerFactoryMockRecorder {
	return m.recorder
}

// NewEmcacheServer mocks base method.
func (m *MockServerFactory) NewEmcacheServer(pgPool *pgxpool.Pool, sqliteBaseDir string, collCache *collectioncache.Manager) protos.EmcacheServiceServer {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewEmcacheServer", pgPool, sqliteBaseDir, collCache)
	ret0, _ := ret[0].(protos.EmcacheServiceServer)
	return ret0
}

// NewEmcacheServer indicates an expected call of NewEmcacheServer.
func (mr *MockServerFactoryMockRecorder) NewEmcacheServer(pgPool, sqliteBaseDir, collCache any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewEmcacheServer", reflect.TypeOf((*MockServerFactory)(nil).NewEmcacheServer), pgPool, sqliteBaseDir, collCache)
}
