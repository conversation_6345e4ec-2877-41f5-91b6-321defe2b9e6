// Code generated by MockGen. DO NOT EDIT.
// Source: internal/grpcapi/server.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db "github.com/nrjais/emcache/internal/db"
	gomock "go.uber.org/mock/gomock"
)

// MockDatabaseOperations is a mock of DatabaseOperations interface.
type MockDatabaseOperations struct {
	ctrl     *gomock.Controller
	recorder *MockDatabaseOperationsMockRecorder
	isgomock struct{}
}

// MockDatabaseOperationsMockRecorder is the mock recorder for MockDatabaseOperations.
type MockDatabaseOperationsMockRecorder struct {
	mock *MockDatabaseOperations
}

// NewMockDatabaseOperations creates a new mock instance.
func NewMockDatabaseOperations(ctrl *gomock.Controller) *MockDatabaseOperations {
	mock := &MockDatabaseOperations{ctrl: ctrl}
	mock.recorder = &MockDatabaseOperationsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatabaseOperations) EXPECT() *MockDatabaseOperationsMockRecorder {
	return m.recorder
}

// AddReplicatedCollection mocks base method.
func (m *MockDatabaseOperations) AddReplicatedCollection(ctx context.Context, pool db.PostgresPool, collectionName string, shapeJSON []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReplicatedCollection", ctx, pool, collectionName, shapeJSON)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReplicatedCollection indicates an expected call of AddReplicatedCollection.
func (mr *MockDatabaseOperationsMockRecorder) AddReplicatedCollection(ctx, pool, collectionName, shapeJSON any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReplicatedCollection", reflect.TypeOf((*MockDatabaseOperations)(nil).AddReplicatedCollection), ctx, pool, collectionName, shapeJSON)
}

// GetAllReplicatedCollectionsWithShapes mocks base method.
func (m *MockDatabaseOperations) GetAllReplicatedCollectionsWithShapes(ctx context.Context, pool db.PostgresPool) ([]db.ReplicatedCollection, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllReplicatedCollectionsWithShapes", ctx, pool)
	ret0, _ := ret[0].([]db.ReplicatedCollection)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllReplicatedCollectionsWithShapes indicates an expected call of GetAllReplicatedCollectionsWithShapes.
func (mr *MockDatabaseOperationsMockRecorder) GetAllReplicatedCollectionsWithShapes(ctx, pool any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllReplicatedCollectionsWithShapes", reflect.TypeOf((*MockDatabaseOperations)(nil).GetAllReplicatedCollectionsWithShapes), ctx, pool)
}

// GetOplogEntriesMultipleCollections mocks base method.
func (m *MockDatabaseOperations) GetOplogEntriesMultipleCollections(ctx context.Context, pool db.PostgresPool, collections []string, afterID int64, limit int) ([]db.OplogEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntriesMultipleCollections", ctx, pool, collections, afterID, limit)
	ret0, _ := ret[0].([]db.OplogEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntriesMultipleCollections indicates an expected call of GetOplogEntriesMultipleCollections.
func (mr *MockDatabaseOperationsMockRecorder) GetOplogEntriesMultipleCollections(ctx, pool, collections, afterID, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntriesMultipleCollections", reflect.TypeOf((*MockDatabaseOperations)(nil).GetOplogEntriesMultipleCollections), ctx, pool, collections, afterID, limit)
}

// RemoveReplicatedCollection mocks base method.
func (m *MockDatabaseOperations) RemoveReplicatedCollection(ctx context.Context, pool db.PostgresPool, collectionName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveReplicatedCollection", ctx, pool, collectionName)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveReplicatedCollection indicates an expected call of RemoveReplicatedCollection.
func (mr *MockDatabaseOperationsMockRecorder) RemoveReplicatedCollection(ctx, pool, collectionName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveReplicatedCollection", reflect.TypeOf((*MockDatabaseOperations)(nil).RemoveReplicatedCollection), ctx, pool, collectionName)
}
