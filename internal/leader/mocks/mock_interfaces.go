// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockLeaderElectorInterface is a mock of LeaderElectorInterface interface.
type MockLeaderElectorInterface struct {
	ctrl     *gomock.Controller
	recorder *MockLeaderElectorInterfaceMockRecorder
	isgomock struct{}
}

// MockLeaderElectorInterfaceMockRecorder is the mock recorder for MockLeaderElectorInterface.
type MockLeaderElectorInterfaceMockRecorder struct {
	mock *MockLeaderElectorInterface
}

// NewMockLeaderElectorInterface creates a new mock instance.
func NewMockLeaderElectorInterface(ctrl *gomock.Controller) *MockLeaderElectorInterface {
	mock := &MockLeaderElectorInterface{ctrl: ctrl}
	mock.recorder = &MockLeaderElectorInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeaderElectorInterface) EXPECT() *MockLeaderElectorInterfaceMockRecorder {
	return m.recorder
}

// ReleaseAll mocks base method.
func (m *MockLeaderElectorInterface) ReleaseAll() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseAll")
}

// ReleaseAll indicates an expected call of ReleaseAll.
func (mr *MockLeaderElectorInterfaceMockRecorder) ReleaseAll() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseAll", reflect.TypeOf((*MockLeaderElectorInterface)(nil).ReleaseAll))
}

// ReleaseLock mocks base method.
func (m *MockLeaderElectorInterface) ReleaseLock(ctx context.Context, collectionName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReleaseLock", ctx, collectionName)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockLeaderElectorInterfaceMockRecorder) ReleaseLock(ctx, collectionName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockLeaderElectorInterface)(nil).ReleaseLock), ctx, collectionName)
}

// TryAcquireLock mocks base method.
func (m *MockLeaderElectorInterface) TryAcquireLock(ctx context.Context, collectionName string, leaseDuration time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryAcquireLock", ctx, collectionName, leaseDuration)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TryAcquireLock indicates an expected call of TryAcquireLock.
func (mr *MockLeaderElectorInterfaceMockRecorder) TryAcquireLock(ctx, collectionName, leaseDuration any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryAcquireLock", reflect.TypeOf((*MockLeaderElectorInterface)(nil).TryAcquireLock), ctx, collectionName, leaseDuration)
}

// MockChangeStreamHandler is a mock of ChangeStreamHandler interface.
type MockChangeStreamHandler struct {
	ctrl     *gomock.Controller
	recorder *MockChangeStreamHandlerMockRecorder
	isgomock struct{}
}

// MockChangeStreamHandlerMockRecorder is the mock recorder for MockChangeStreamHandler.
type MockChangeStreamHandlerMockRecorder struct {
	mock *MockChangeStreamHandler
}

// NewMockChangeStreamHandler creates a new mock instance.
func NewMockChangeStreamHandler(ctrl *gomock.Controller) *MockChangeStreamHandler {
	mock := &MockChangeStreamHandler{ctrl: ctrl}
	mock.recorder = &MockChangeStreamHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChangeStreamHandler) EXPECT() *MockChangeStreamHandlerMockRecorder {
	return m.recorder
}

// StartLeaderWork mocks base method.
func (m *MockChangeStreamHandler) StartLeaderWork(ctx context.Context, collectionName string, leaseDuration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartLeaderWork", ctx, collectionName, leaseDuration)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartLeaderWork indicates an expected call of StartLeaderWork.
func (mr *MockChangeStreamHandlerMockRecorder) StartLeaderWork(ctx, collectionName, leaseDuration any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartLeaderWork", reflect.TypeOf((*MockChangeStreamHandler)(nil).StartLeaderWork), ctx, collectionName, leaseDuration)
}
