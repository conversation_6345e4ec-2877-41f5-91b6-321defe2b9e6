// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db "github.com/nrjais/emcache/internal/db"
	shape "github.com/nrjais/emcache/internal/shape"
	gomock "go.uber.org/mock/gomock"
)

// MockPostgresPool is a mock of PostgresPool interface.
type MockPostgresPool struct {
	ctrl     *gomock.Controller
	recorder *MockPostgresPoolMockRecorder
	isgomock struct{}
}

// MockPostgresPoolMockRecorder is the mock recorder for MockPostgresPool.
type MockPostgresPoolMockRecorder struct {
	mock *MockPostgresPool
}

// NewMockPostgresPool creates a new mock instance.
func NewMockPostgresPool(ctrl *gomock.Controller) *MockPostgresPool {
	mock := &MockPostgresPool{ctrl: ctrl}
	mock.recorder = &MockPostgresPoolMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPostgresPool) EXPECT() *MockPostgresPoolMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockPostgresPool) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockPostgresPoolMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockPostgresPool)(nil).Close))
}

// Exec mocks base method.
func (m *MockPostgresPool) Exec(ctx context.Context, sql string, args ...any) (db.CommandTag, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, sql}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Exec", varargs...)
	ret0, _ := ret[0].(db.CommandTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exec indicates an expected call of Exec.
func (mr *MockPostgresPoolMockRecorder) Exec(ctx, sql any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, sql}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exec", reflect.TypeOf((*MockPostgresPool)(nil).Exec), varargs...)
}

// Query mocks base method.
func (m *MockPostgresPool) Query(ctx context.Context, sql string, args ...any) (db.Rows, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, sql}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Query", varargs...)
	ret0, _ := ret[0].(db.Rows)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockPostgresPoolMockRecorder) Query(ctx, sql any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, sql}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockPostgresPool)(nil).Query), varargs...)
}

// QueryRow mocks base method.
func (m *MockPostgresPool) QueryRow(ctx context.Context, sql string, args ...any) db.Row {
	m.ctrl.T.Helper()
	varargs := []any{ctx, sql}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryRow", varargs...)
	ret0, _ := ret[0].(db.Row)
	return ret0
}

// QueryRow indicates an expected call of QueryRow.
func (mr *MockPostgresPoolMockRecorder) QueryRow(ctx, sql any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, sql}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryRow", reflect.TypeOf((*MockPostgresPool)(nil).QueryRow), varargs...)
}

// MockRows is a mock of Rows interface.
type MockRows struct {
	ctrl     *gomock.Controller
	recorder *MockRowsMockRecorder
	isgomock struct{}
}

// MockRowsMockRecorder is the mock recorder for MockRows.
type MockRowsMockRecorder struct {
	mock *MockRows
}

// NewMockRows creates a new mock instance.
func NewMockRows(ctrl *gomock.Controller) *MockRows {
	mock := &MockRows{ctrl: ctrl}
	mock.recorder = &MockRowsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRows) EXPECT() *MockRowsMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockRows) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockRowsMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockRows)(nil).Close))
}

// Err mocks base method.
func (m *MockRows) Err() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Err")
	ret0, _ := ret[0].(error)
	return ret0
}

// Err indicates an expected call of Err.
func (mr *MockRowsMockRecorder) Err() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Err", reflect.TypeOf((*MockRows)(nil).Err))
}

// Next mocks base method.
func (m *MockRows) Next() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Next")
	ret0, _ := ret[0].(bool)
	return ret0
}

// Next indicates an expected call of Next.
func (mr *MockRowsMockRecorder) Next() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Next", reflect.TypeOf((*MockRows)(nil).Next))
}

// Scan mocks base method.
func (m *MockRows) Scan(dest ...any) error {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range dest {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Scan", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Scan indicates an expected call of Scan.
func (mr *MockRowsMockRecorder) Scan(dest ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockRows)(nil).Scan), dest...)
}

// MockRow is a mock of Row interface.
type MockRow struct {
	ctrl     *gomock.Controller
	recorder *MockRowMockRecorder
	isgomock struct{}
}

// MockRowMockRecorder is the mock recorder for MockRow.
type MockRowMockRecorder struct {
	mock *MockRow
}

// NewMockRow creates a new mock instance.
func NewMockRow(ctrl *gomock.Controller) *MockRow {
	mock := &MockRow{ctrl: ctrl}
	mock.recorder = &MockRowMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRow) EXPECT() *MockRowMockRecorder {
	return m.recorder
}

// Scan mocks base method.
func (m *MockRow) Scan(dest ...any) error {
	m.ctrl.T.Helper()
	varargs := []any{}
	for _, a := range dest {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Scan", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Scan indicates an expected call of Scan.
func (mr *MockRowMockRecorder) Scan(dest ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockRow)(nil).Scan), dest...)
}

// MockCommandTag is a mock of CommandTag interface.
type MockCommandTag struct {
	ctrl     *gomock.Controller
	recorder *MockCommandTagMockRecorder
	isgomock struct{}
}

// MockCommandTagMockRecorder is the mock recorder for MockCommandTag.
type MockCommandTagMockRecorder struct {
	mock *MockCommandTag
}

// NewMockCommandTag creates a new mock instance.
func NewMockCommandTag(ctrl *gomock.Controller) *MockCommandTag {
	mock := &MockCommandTag{ctrl: ctrl}
	mock.recorder = &MockCommandTagMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCommandTag) EXPECT() *MockCommandTagMockRecorder {
	return m.recorder
}

// RowsAffected mocks base method.
func (m *MockCommandTag) RowsAffected() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RowsAffected")
	ret0, _ := ret[0].(int64)
	return ret0
}

// RowsAffected indicates an expected call of RowsAffected.
func (mr *MockCommandTagMockRecorder) RowsAffected() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RowsAffected", reflect.TypeOf((*MockCommandTag)(nil).RowsAffected))
}

// MockOplogRepository is a mock of OplogRepository interface.
type MockOplogRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOplogRepositoryMockRecorder
	isgomock struct{}
}

// MockOplogRepositoryMockRecorder is the mock recorder for MockOplogRepository.
type MockOplogRepositoryMockRecorder struct {
	mock *MockOplogRepository
}

// NewMockOplogRepository creates a new mock instance.
func NewMockOplogRepository(ctrl *gomock.Controller) *MockOplogRepository {
	mock := &MockOplogRepository{ctrl: ctrl}
	mock.recorder = &MockOplogRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOplogRepository) EXPECT() *MockOplogRepositoryMockRecorder {
	return m.recorder
}

// GetOplogEntriesGlobal mocks base method.
func (m *MockOplogRepository) GetOplogEntriesGlobal(ctx context.Context, afterID int64, limit int) ([]db.OplogEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntriesGlobal", ctx, afterID, limit)
	ret0, _ := ret[0].([]db.OplogEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntriesGlobal indicates an expected call of GetOplogEntriesGlobal.
func (mr *MockOplogRepositoryMockRecorder) GetOplogEntriesGlobal(ctx, afterID, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntriesGlobal", reflect.TypeOf((*MockOplogRepository)(nil).GetOplogEntriesGlobal), ctx, afterID, limit)
}

// GetOplogEntriesMultipleCollections mocks base method.
func (m *MockOplogRepository) GetOplogEntriesMultipleCollections(ctx context.Context, collections []string, afterID int64, limit int) ([]db.OplogEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntriesMultipleCollections", ctx, collections, afterID, limit)
	ret0, _ := ret[0].([]db.OplogEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntriesMultipleCollections indicates an expected call of GetOplogEntriesMultipleCollections.
func (mr *MockOplogRepositoryMockRecorder) GetOplogEntriesMultipleCollections(ctx, collections, afterID, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntriesMultipleCollections", reflect.TypeOf((*MockOplogRepository)(nil).GetOplogEntriesMultipleCollections), ctx, collections, afterID, limit)
}

// InsertOplogEntry mocks base method.
func (m *MockOplogRepository) InsertOplogEntry(ctx context.Context, entry db.OplogEntry) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertOplogEntry", ctx, entry)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertOplogEntry indicates an expected call of InsertOplogEntry.
func (mr *MockOplogRepositoryMockRecorder) InsertOplogEntry(ctx, entry any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertOplogEntry", reflect.TypeOf((*MockOplogRepository)(nil).InsertOplogEntry), ctx, entry)
}

// MockCollectionRepository is a mock of CollectionRepository interface.
type MockCollectionRepository struct {
	ctrl     *gomock.Controller
	recorder *MockCollectionRepositoryMockRecorder
	isgomock struct{}
}

// MockCollectionRepositoryMockRecorder is the mock recorder for MockCollectionRepository.
type MockCollectionRepositoryMockRecorder struct {
	mock *MockCollectionRepository
}

// NewMockCollectionRepository creates a new mock instance.
func NewMockCollectionRepository(ctrl *gomock.Controller) *MockCollectionRepository {
	mock := &MockCollectionRepository{ctrl: ctrl}
	mock.recorder = &MockCollectionRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCollectionRepository) EXPECT() *MockCollectionRepositoryMockRecorder {
	return m.recorder
}

// AddReplicatedCollection mocks base method.
func (m *MockCollectionRepository) AddReplicatedCollection(ctx context.Context, name string, collShape shape.Shape) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReplicatedCollection", ctx, name, collShape)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReplicatedCollection indicates an expected call of AddReplicatedCollection.
func (mr *MockCollectionRepositoryMockRecorder) AddReplicatedCollection(ctx, name, collShape any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReplicatedCollection", reflect.TypeOf((*MockCollectionRepository)(nil).AddReplicatedCollection), ctx, name, collShape)
}

// GetAllCurrentCollectionVersions mocks base method.
func (m *MockCollectionRepository) GetAllCurrentCollectionVersions(ctx context.Context) ([]db.CollectionVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCurrentCollectionVersions", ctx)
	ret0, _ := ret[0].([]db.CollectionVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCurrentCollectionVersions indicates an expected call of GetAllCurrentCollectionVersions.
func (mr *MockCollectionRepositoryMockRecorder) GetAllCurrentCollectionVersions(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCurrentCollectionVersions", reflect.TypeOf((*MockCollectionRepository)(nil).GetAllCurrentCollectionVersions), ctx)
}

// GetReplicatedCollection mocks base method.
func (m *MockCollectionRepository) GetReplicatedCollection(ctx context.Context, name string) (db.ReplicatedCollection, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReplicatedCollection", ctx, name)
	ret0, _ := ret[0].(db.ReplicatedCollection)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetReplicatedCollection indicates an expected call of GetReplicatedCollection.
func (mr *MockCollectionRepositoryMockRecorder) GetReplicatedCollection(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReplicatedCollection", reflect.TypeOf((*MockCollectionRepository)(nil).GetReplicatedCollection), ctx, name)
}

// ListReplicatedCollections mocks base method.
func (m *MockCollectionRepository) ListReplicatedCollections(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReplicatedCollections", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReplicatedCollections indicates an expected call of ListReplicatedCollections.
func (mr *MockCollectionRepositoryMockRecorder) ListReplicatedCollections(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReplicatedCollections", reflect.TypeOf((*MockCollectionRepository)(nil).ListReplicatedCollections), ctx)
}

// RemoveReplicatedCollection mocks base method.
func (m *MockCollectionRepository) RemoveReplicatedCollection(ctx context.Context, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveReplicatedCollection", ctx, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveReplicatedCollection indicates an expected call of RemoveReplicatedCollection.
func (mr *MockCollectionRepositoryMockRecorder) RemoveReplicatedCollection(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveReplicatedCollection", reflect.TypeOf((*MockCollectionRepository)(nil).RemoveReplicatedCollection), ctx, name)
}

// UpdateCollectionShape mocks base method.
func (m *MockCollectionRepository) UpdateCollectionShape(ctx context.Context, name string, newShape shape.Shape, newVersion int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCollectionShape", ctx, name, newShape, newVersion)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCollectionShape indicates an expected call of UpdateCollectionShape.
func (mr *MockCollectionRepositoryMockRecorder) UpdateCollectionShape(ctx, name, newShape, newVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCollectionShape", reflect.TypeOf((*MockCollectionRepository)(nil).UpdateCollectionShape), ctx, name, newShape, newVersion)
}

// MockResumeTokenRepository is a mock of ResumeTokenRepository interface.
type MockResumeTokenRepository struct {
	ctrl     *gomock.Controller
	recorder *MockResumeTokenRepositoryMockRecorder
	isgomock struct{}
}

// MockResumeTokenRepositoryMockRecorder is the mock recorder for MockResumeTokenRepository.
type MockResumeTokenRepositoryMockRecorder struct {
	mock *MockResumeTokenRepository
}

// NewMockResumeTokenRepository creates a new mock instance.
func NewMockResumeTokenRepository(ctrl *gomock.Controller) *MockResumeTokenRepository {
	mock := &MockResumeTokenRepository{ctrl: ctrl}
	mock.recorder = &MockResumeTokenRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResumeTokenRepository) EXPECT() *MockResumeTokenRepositoryMockRecorder {
	return m.recorder
}

// GetResumeToken mocks base method.
func (m *MockResumeTokenRepository) GetResumeToken(ctx context.Context, collection string) (string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResumeToken", ctx, collection)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetResumeToken indicates an expected call of GetResumeToken.
func (mr *MockResumeTokenRepositoryMockRecorder) GetResumeToken(ctx, collection any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResumeToken", reflect.TypeOf((*MockResumeTokenRepository)(nil).GetResumeToken), ctx, collection)
}

// UpsertResumeToken mocks base method.
func (m *MockResumeTokenRepository) UpsertResumeToken(ctx context.Context, collection, token string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertResumeToken", ctx, collection, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertResumeToken indicates an expected call of UpsertResumeToken.
func (mr *MockResumeTokenRepositoryMockRecorder) UpsertResumeToken(ctx, collection, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertResumeToken", reflect.TypeOf((*MockResumeTokenRepository)(nil).UpsertResumeToken), ctx, collection, token)
}

// MockDatabase is a mock of Database interface.
type MockDatabase struct {
	ctrl     *gomock.Controller
	recorder *MockDatabaseMockRecorder
	isgomock struct{}
}

// MockDatabaseMockRecorder is the mock recorder for MockDatabase.
type MockDatabaseMockRecorder struct {
	mock *MockDatabase
}

// NewMockDatabase creates a new mock instance.
func NewMockDatabase(ctrl *gomock.Controller) *MockDatabase {
	mock := &MockDatabase{ctrl: ctrl}
	mock.recorder = &MockDatabaseMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDatabase) EXPECT() *MockDatabaseMockRecorder {
	return m.recorder
}

// AddReplicatedCollection mocks base method.
func (m *MockDatabase) AddReplicatedCollection(ctx context.Context, name string, collShape shape.Shape) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddReplicatedCollection", ctx, name, collShape)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddReplicatedCollection indicates an expected call of AddReplicatedCollection.
func (mr *MockDatabaseMockRecorder) AddReplicatedCollection(ctx, name, collShape any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddReplicatedCollection", reflect.TypeOf((*MockDatabase)(nil).AddReplicatedCollection), ctx, name, collShape)
}

// GetAllCurrentCollectionVersions mocks base method.
func (m *MockDatabase) GetAllCurrentCollectionVersions(ctx context.Context) ([]db.CollectionVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCurrentCollectionVersions", ctx)
	ret0, _ := ret[0].([]db.CollectionVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCurrentCollectionVersions indicates an expected call of GetAllCurrentCollectionVersions.
func (mr *MockDatabaseMockRecorder) GetAllCurrentCollectionVersions(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCurrentCollectionVersions", reflect.TypeOf((*MockDatabase)(nil).GetAllCurrentCollectionVersions), ctx)
}

// GetOplogEntriesGlobal mocks base method.
func (m *MockDatabase) GetOplogEntriesGlobal(ctx context.Context, afterID int64, limit int) ([]db.OplogEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntriesGlobal", ctx, afterID, limit)
	ret0, _ := ret[0].([]db.OplogEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntriesGlobal indicates an expected call of GetOplogEntriesGlobal.
func (mr *MockDatabaseMockRecorder) GetOplogEntriesGlobal(ctx, afterID, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntriesGlobal", reflect.TypeOf((*MockDatabase)(nil).GetOplogEntriesGlobal), ctx, afterID, limit)
}

// GetOplogEntriesMultipleCollections mocks base method.
func (m *MockDatabase) GetOplogEntriesMultipleCollections(ctx context.Context, collections []string, afterID int64, limit int) ([]db.OplogEntry, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOplogEntriesMultipleCollections", ctx, collections, afterID, limit)
	ret0, _ := ret[0].([]db.OplogEntry)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOplogEntriesMultipleCollections indicates an expected call of GetOplogEntriesMultipleCollections.
func (mr *MockDatabaseMockRecorder) GetOplogEntriesMultipleCollections(ctx, collections, afterID, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOplogEntriesMultipleCollections", reflect.TypeOf((*MockDatabase)(nil).GetOplogEntriesMultipleCollections), ctx, collections, afterID, limit)
}

// GetReplicatedCollection mocks base method.
func (m *MockDatabase) GetReplicatedCollection(ctx context.Context, name string) (db.ReplicatedCollection, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReplicatedCollection", ctx, name)
	ret0, _ := ret[0].(db.ReplicatedCollection)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetReplicatedCollection indicates an expected call of GetReplicatedCollection.
func (mr *MockDatabaseMockRecorder) GetReplicatedCollection(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReplicatedCollection", reflect.TypeOf((*MockDatabase)(nil).GetReplicatedCollection), ctx, name)
}

// GetResumeToken mocks base method.
func (m *MockDatabase) GetResumeToken(ctx context.Context, collection string) (string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResumeToken", ctx, collection)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetResumeToken indicates an expected call of GetResumeToken.
func (mr *MockDatabaseMockRecorder) GetResumeToken(ctx, collection any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResumeToken", reflect.TypeOf((*MockDatabase)(nil).GetResumeToken), ctx, collection)
}

// InsertOplogEntry mocks base method.
func (m *MockDatabase) InsertOplogEntry(ctx context.Context, entry db.OplogEntry) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertOplogEntry", ctx, entry)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InsertOplogEntry indicates an expected call of InsertOplogEntry.
func (mr *MockDatabaseMockRecorder) InsertOplogEntry(ctx, entry any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertOplogEntry", reflect.TypeOf((*MockDatabase)(nil).InsertOplogEntry), ctx, entry)
}

// ListReplicatedCollections mocks base method.
func (m *MockDatabase) ListReplicatedCollections(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReplicatedCollections", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReplicatedCollections indicates an expected call of ListReplicatedCollections.
func (mr *MockDatabaseMockRecorder) ListReplicatedCollections(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReplicatedCollections", reflect.TypeOf((*MockDatabase)(nil).ListReplicatedCollections), ctx)
}

// RemoveReplicatedCollection mocks base method.
func (m *MockDatabase) RemoveReplicatedCollection(ctx context.Context, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveReplicatedCollection", ctx, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveReplicatedCollection indicates an expected call of RemoveReplicatedCollection.
func (mr *MockDatabaseMockRecorder) RemoveReplicatedCollection(ctx, name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveReplicatedCollection", reflect.TypeOf((*MockDatabase)(nil).RemoveReplicatedCollection), ctx, name)
}

// UpdateCollectionShape mocks base method.
func (m *MockDatabase) UpdateCollectionShape(ctx context.Context, name string, newShape shape.Shape, newVersion int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCollectionShape", ctx, name, newShape, newVersion)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCollectionShape indicates an expected call of UpdateCollectionShape.
func (mr *MockDatabaseMockRecorder) UpdateCollectionShape(ctx, name, newShape, newVersion any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCollectionShape", reflect.TypeOf((*MockDatabase)(nil).UpdateCollectionShape), ctx, name, newShape, newVersion)
}

// UpsertResumeToken mocks base method.
func (m *MockDatabase) UpsertResumeToken(ctx context.Context, collection, token string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertResumeToken", ctx, collection, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertResumeToken indicates an expected call of UpsertResumeToken.
func (mr *MockDatabaseMockRecorder) UpsertResumeToken(ctx, collection, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertResumeToken", reflect.TypeOf((*MockDatabase)(nil).UpsertResumeToken), ctx, collection, token)
}
