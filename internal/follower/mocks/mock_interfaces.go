// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go
//
// Generated by this command:
//
//	mockgen -source=interfaces.go -destination=mocks/mock_interfaces.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	sync "sync"

	gomock "go.uber.org/mock/gomock"
)

// MockFollowerInterface is a mock of FollowerInterface interface.
type MockFollowerInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFollowerInterfaceMockRecorder
	isgomock struct{}
}

// MockFollowerInterfaceMockRecorder is the mock recorder for MockFollowerInterface.
type MockFollowerInterfaceMockRecorder struct {
	mock *MockFollowerInterface
}

// NewMockFollowerInterface creates a new mock instance.
func NewMockFollowerInterface(ctrl *gomock.Controller) *MockFollowerInterface {
	mock := &MockFollowerInterface{ctrl: ctrl}
	mock.recorder = &MockFollowerInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFollowerInterface) EXPECT() *MockFollowerInterfaceMockRecorder {
	return m.recorder
}

// EnsureOpenConnection mocks base method.
func (m *MockFollowerInterface) EnsureOpenConnection(ctx context.Context, collectionName string, version int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnsureOpenConnection", ctx, collectionName, version)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnsureOpenConnection indicates an expected call of EnsureOpenConnection.
func (mr *MockFollowerInterfaceMockRecorder) EnsureOpenConnection(ctx, collectionName, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureOpenConnection", reflect.TypeOf((*MockFollowerInterface)(nil).EnsureOpenConnection), ctx, collectionName, version)
}

// Start mocks base method.
func (m *MockFollowerInterface) Start(ctx context.Context, wg *sync.WaitGroup) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Start", ctx, wg)
}

// Start indicates an expected call of Start.
func (mr *MockFollowerInterfaceMockRecorder) Start(ctx, wg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockFollowerInterface)(nil).Start), ctx, wg)
}
