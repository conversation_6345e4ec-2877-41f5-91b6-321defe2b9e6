[package]
name = "emcache-server"
version = "0.1.0"
edition = "2021"
authors = ["Emcache Team"]
description = "High-performance MongoDB to SQLite replication server written in Rust"
license = "MIT"

[[bin]]
name = "emcache-server"
path = "src/main.rs"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"

# gRPC and protobuf
tonic = "0.12"
prost = "0.13"
prost-types = "0.13"

# Database connectivity
sqlx = { version = "0.8", features = [
  "runtime-tokio-rustls",
  "postgres",
  "sqlite",
  "chrono",
  "uuid",
  "json",
] }
mongodb = "3.2"
bson = "2.11"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Configuration
config = "0.14"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"
futures-util = "0.3"
bytes = "1.0"
async-trait = "0.1"

# Compression
zstd = "0.13"
flate2 = "1.0"

# Validation
validator = { version = "0.18", features = ["derive"] }

# Concurrency
parking_lot = "0.12"
dashmap = "6.0"

# File system operations
tempfile = "3.0"

# Signal handling
signal-hook = "0.3"
signal-hook-tokio = { version = "0.3", features = ["futures-v0_3"] }

# System utilities
hostname = "0.4"

[build-dependencies]
tonic-build = "0.12"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.13"
