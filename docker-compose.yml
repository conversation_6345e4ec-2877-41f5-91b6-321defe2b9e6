services:
  postgres:
    image: postgres:17
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
  mongo:
    image: mongo:8
    ports:
      - "27017:27017"
    extra_hosts:
      - "mongo.emcache.orb.local:host-gateway"
    volumes:
      - mongo_data:/data
    command: ["--replSet", "rs0", "--bind_ip_all", "--port", "27017"]
    healthcheck:
      test: echo "try { rs.status() } catch (err) { rs.initiate({_id:'rs0',members:[{_id:0,host:'mongo.emcache.orb.local:27017'}]}) }" | mongosh --port 27017 --quiet
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

volumes:
  postgres_data:
  mongo_data:
