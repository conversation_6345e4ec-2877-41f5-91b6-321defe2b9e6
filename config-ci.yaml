# CI Configuration for Emcache
# This config is used in GitHub Actions CI environment

# PostgreSQL connection URL for CI
postgres_url: "postgres://postgres:password@localhost:5432/postgres?sslmode=disable&pool_max_conns=1000"

# MongoDB connection URL for CI (must include database name)
mongo_url: "mongodb://localhost:27017/test?directConnection=true"

# gRPC server listen address
grpc_port: ":50051"

# Directory to store SQLite database files
sqlite_dir: "./emcache_dbs"

# Logging level for CI (more verbose for debugging)
log_level: "DEBUG"

# Coordinator operation settings (faster intervals for CI)
coordinator:
  collection_refresh_interval_secs: 1

# Leader operation settings (faster intervals for CI)
leader:
  resume_token_update_interval_secs: 1
  initial_scan_batch_size: 100
  lease_duration_secs: 5

# Follower operation settings (faster intervals for CI)
follower:
  poll_interval_secs: 1
  batch_size: 50
  cleanup_interval_secs: 10

# Snapshot settings (shorter TTL for CI)
snapshot:
  ttl_secs: 10
