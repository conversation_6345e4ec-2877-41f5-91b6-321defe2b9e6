# Example configuration for Emcache

# Required: PostgreSQL connection URL
# Currently locking blocks one connection per collection, so we need to increase the pool size
postgres_url: "postgres://postgres:<EMAIL>:5432/postgres?sslmode=disable&pool_max_conns=1000"

# Required: MongoDB connection URL (must include database name)
mongo_url: "mongodb://mongo.emcache.orb.local:27017/test?directConnection=true"

# Optional: gRPC server listen address
grpc_port: ":50051"

# Optional: Directory to store SQLite database files
sqlite_dir: "./emcache_dbs"

# Optional: Logging level (DEBUG, INFO, WARN, ERROR)
log_level: "INFO"

# Optional: Coordinator operation settings
coordinator:
  # How often to check for new collections to manage (seconds)
  collection_refresh_interval_secs: 1

# Optional: Leader operation settings
leader:
  # How often to save the resume token (seconds)
  resume_token_update_interval_secs: 1
  # How many documents to fetch at once during initial collection scan
  initial_scan_batch_size: 1000
  # How long the leader should hold the lease (seconds)
  lease_duration_secs: 5

# Optional: Follower operation settings
follower:
  # How often the follower checks Postgres for new oplog entries (seconds)
  poll_interval_secs: 1
  # How many oplog entries the follower fetches from Postgres at a time
  batch_size: 100
  # How often to clean up old SQLite database files (seconds)
  cleanup_interval_secs: 10

# Optional: Snapshot settings
snapshot:
  # How long an unused snapshot file is kept before deletion (seconds)
  ttl_secs: 10
